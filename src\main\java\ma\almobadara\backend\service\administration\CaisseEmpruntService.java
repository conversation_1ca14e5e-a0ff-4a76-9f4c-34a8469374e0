package ma.almobadara.backend.service.administration;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.CaisseEmpruntDto;
import ma.almobadara.backend.dto.administration.CaisseEmpruntDetailDto;
import ma.almobadara.backend.dto.administration.CaisseEmpruntHistoryDto;
import ma.almobadara.backend.dto.administration.CaisseEmpruntListDto;
import ma.almobadara.backend.dto.donation.BudgetLineDTO;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.donor.DonorDTO;
import ma.almobadara.backend.dto.referentiel.CanalDonationDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.mapper.CaisseEmpruntMapper;
import ma.almobadara.backend.mapper.CaisseEmpruntHistoryMapper;
import ma.almobadara.backend.mapper.DonorMapper;
import ma.almobadara.backend.model.administration.CaisseEmprunt;
import ma.almobadara.backend.model.administration.CaisseEmpruntHistory;
import ma.almobadara.backend.model.donor.Donor;
import ma.almobadara.backend.model.donor.DonorPhysical;
import ma.almobadara.backend.model.donor.DonorMoral;
import ma.almobadara.backend.model.donor.DonorAnonyme;
import ma.almobadara.backend.repository.administration.CaisseEmpruntRepository;
import ma.almobadara.backend.repository.administration.CaisseEmpruntHistoryRepository;
import ma.almobadara.backend.repository.donor.DonorRepository;
import ma.almobadara.backend.service.Donation.DonationService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static ma.almobadara.backend.util.times.DateUtils.convertToDate;

@Service
@AllArgsConstructor
@Slf4j
public class CaisseEmpruntService {
    private final CaisseEmpruntMapper caisseEmpruntMapper;
    private final CaisseEmpruntHistoryMapper caisseEmpruntHistoryMapper;
    private final CaisseEmpruntRepository caisseEmpruntRepository;
    private final CaisseEmpruntHistoryRepository caisseEmpruntHistoryRepository;
    private final DonorRepository donorRepository;
    private final EntityManager entityManager;
    private final DonorMapper donorMapper;
    private final DonationService donationService;


    public CaisseEmpruntDto createCaisseEmprunt(CaisseEmpruntDto caisseEmpruntDto) throws TechnicalException, IOException {
        log.info("Creating Caisse Emprunt: {}", caisseEmpruntDto);

        Donor donor = donorRepository.findById(caisseEmpruntDto.getDonorId())
                .orElseThrow(() -> new TechnicalException("Donor not found with id: " + caisseEmpruntDto.getDonorId()));

        // Check if CaisseEmprunt already exists for this donor
        Optional<CaisseEmprunt> existingCaisseEmprunt = caisseEmpruntRepository.findByDonorId(caisseEmpruntDto.getDonorId());

        CaisseEmprunt caisseEmprunt;
        if (existingCaisseEmprunt.isPresent()) {
            // Update existing CaisseEmprunt
            caisseEmprunt = existingCaisseEmprunt.get();
            caisseEmprunt.setGlobalAmount(caisseEmpruntDto.getGlobalAmount()+ caisseEmprunt.getGlobalAmount());
            caisseEmprunt.setLastDateEmprunt(caisseEmpruntDto.getLastDateEmprunt());
            caisseEmprunt.setStatus(caisseEmpruntDto.getStatus() != null ? caisseEmpruntDto.getStatus() : "ACTIVE");
        } else {
            // Create new CaisseEmprunt
            caisseEmprunt = caisseEmpruntMapper.toEntity(caisseEmpruntDto);
            caisseEmprunt.setDonor(donor);
            caisseEmprunt.setStatus(caisseEmpruntDto.getStatus() != null ? caisseEmpruntDto.getStatus() : "ACTIVE");
        }
        CaisseEmpruntHistoryDto caisseEmpruntHistoryDto=
                CaisseEmpruntHistoryDto
                        .builder()
                        .amount(caisseEmpruntDto.getGlobalAmount())
                        .dateEmprunt(caisseEmpruntDto.getLastDateEmprunt())
                        .type("EMPRUNT")
                        .build();

        caisseEmprunt = caisseEmpruntRepository.save(caisseEmprunt);
        addEmpruntHistory(caisseEmprunt.getId(), caisseEmpruntHistoryDto);
        addDonation(caisseEmprunt);
        log.info("Caisse Emprunt created/updated successfully: {}", caisseEmprunt);
        return caisseEmpruntMapper.toDto(caisseEmprunt);
    }
    public CaisseEmpruntDto createCaisseRemboursement(CaisseEmpruntDto caisseEmpruntDto) throws TechnicalException, IOException {
        log.info("Creating Caisse Remboursement: {}", caisseEmpruntDto);

        // Check if CaisseEmprunt already exists for this donor
        Optional<CaisseEmprunt> existingCaisseEmprunt = caisseEmpruntRepository.findByDonorId(caisseEmpruntDto.getDonorId());

        CaisseEmprunt caisseEmprunt=null;
        if (existingCaisseEmprunt.isPresent()) {
            caisseEmprunt = existingCaisseEmprunt.get();
            caisseEmprunt.setGlobalAmount(caisseEmprunt.getGlobalAmount() - caisseEmpruntDto.getGlobalAmount());
            caisseEmprunt.setLastDateRemboursement(caisseEmpruntDto.getLastDateRemboursement());
         }
        else{
            throw new TechnicalException("Caisse Emprunt not found for donor with id: " + caisseEmpruntDto.getDonorId());
        }
        CaisseEmpruntHistoryDto caisseEmpruntHistoryDto=
                CaisseEmpruntHistoryDto
                        .builder()
                        .amount(caisseEmpruntDto.getGlobalAmount())
                        .dateRemboursement(caisseEmpruntDto.getLastDateRemboursement())
                        .type("REMBOURSEMENT")
                        .build();

        caisseEmprunt = caisseEmpruntRepository.save(caisseEmprunt);
        addRemboursementHistory(caisseEmprunt.getId(), caisseEmpruntHistoryDto);
        addDonation(caisseEmprunt);
        log.info("Caisse Emprunt created/updated successfully: {}", caisseEmprunt);
        return caisseEmpruntMapper.toDto(caisseEmprunt);
    }
    public CaisseEmpruntHistoryDto addEmpruntHistory(Long caisseEmpruntId, CaisseEmpruntHistoryDto historyDto) throws TechnicalException {
        log.info("Adding emprunt history for CaisseEmprunt ID: {}", caisseEmpruntId);

        CaisseEmprunt caisseEmprunt = caisseEmpruntRepository.findById(caisseEmpruntId)
                .orElseThrow(() -> new TechnicalException("CaisseEmprunt not found with ID: " + caisseEmpruntId));

        CaisseEmpruntHistory history = caisseEmpruntHistoryMapper.toEntity(historyDto);
        history.setCaisseEmprunt(caisseEmprunt);
        history.setType("EMPRUNT");

        history = caisseEmpruntHistoryRepository.save(history);


        log.info("Emprunt history added successfully: {}", history);
        return caisseEmpruntHistoryMapper.toDto(history);
    }

    public CaisseEmpruntHistoryDto addRemboursementHistory(Long caisseEmpruntId, CaisseEmpruntHistoryDto historyDto) throws TechnicalException {
        log.info("Adding remboursement history for CaisseEmprunt ID: {}", caisseEmpruntId);

        CaisseEmprunt caisseEmprunt = caisseEmpruntRepository.findById(caisseEmpruntId)
                .orElseThrow(() -> new TechnicalException("CaisseEmprunt not found with ID: " + caisseEmpruntId));

        CaisseEmpruntHistory history = caisseEmpruntHistoryMapper.toEntity(historyDto);
        history.setCaisseEmprunt(caisseEmprunt);
        history.setType("REMBOURSEMENT");

        history = caisseEmpruntHistoryRepository.save(history);

        log.info("Remboursement history added successfully: {}", history);
        return caisseEmpruntHistoryMapper.toDto(history);
    }

    private void updateCaisseEmpruntFromHistory(CaisseEmprunt caisseEmprunt) {
        List<CaisseEmpruntHistory> histories = caisseEmpruntHistoryRepository.findByCaisseEmpruntIdOrderByCreationDateDesc(caisseEmprunt.getId());

        if (!histories.isEmpty()) {
            // Update last dates
            histories.stream()
                    .filter(h -> "EMPRUNT".equals(h.getType()) && h.getDateEmprunt() != null)
                    .findFirst()
                    .ifPresent(h -> caisseEmprunt.setLastDateEmprunt(h.getDateEmprunt()));

            histories.stream()
                    .filter(h -> "REMBOURSEMENT".equals(h.getType()) && h.getDateRemboursement() != null)
                    .findFirst()
                    .ifPresent(h -> caisseEmprunt.setLastDateRemboursement(h.getDateRemboursement()));

            // Calculate global amount (sum of all emprunts minus remboursements)
            double totalEmprunts = histories.stream()
                    .mapToDouble(CaisseEmpruntHistory::getAmount)
                    .sum();

            double totalRemboursements = histories.stream()
                    .filter(h -> "REMBOURSEMENT".equals(h.getType()))
                    .mapToDouble(CaisseEmpruntHistory::getAmount)
                    .sum();

            caisseEmprunt.setGlobalAmount(totalEmprunts - totalRemboursements);
        }

        caisseEmpruntRepository.save(caisseEmprunt);
    }

    public CaisseEmpruntDto getCaisseEmpruntById(Long id) throws TechnicalException {
        log.info("Getting CaisseEmprunt by ID: {}", id);

        if (id == null) {
            throw new TechnicalException("CaisseEmprunt ID cannot be null");
        }

        CaisseEmprunt caisseEmprunt = caisseEmpruntRepository.findById(id)
                .orElseThrow(() -> new TechnicalException("CaisseEmprunt not found with ID: " + id));

        CaisseEmpruntDto dto = caisseEmpruntMapper.toDto(caisseEmprunt);

        // Add histories
        List<CaisseEmpruntHistory> histories = caisseEmpruntHistoryRepository.findByCaisseEmpruntIdOrderByCreationDateDesc(id);
        List<CaisseEmpruntHistoryDto> historyDtos = histories.stream()
                .map(caisseEmpruntHistoryMapper::toDto)
                .collect(Collectors.toList());
        dto.setHistories(historyDtos);

        log.info("Successfully retrieved CaisseEmprunt with ID: {}", id);
        return dto;
    }

    public CaisseEmpruntDetailDto getCaisseEmpruntDetailById(Long id) throws TechnicalException {
        log.info("Getting detailed CaisseEmprunt by ID: {}", id);

        if (id == null) {
            throw new TechnicalException("CaisseEmprunt ID cannot be null");
        }

        CaisseEmprunt caisseEmprunt = caisseEmpruntRepository.findById(id)
                .orElseThrow(() -> new TechnicalException("CaisseEmprunt not found with ID: " + id));

        CaisseEmpruntDetailDto dto = caisseEmpruntMapper.toDetailDto(caisseEmprunt);

        // Set detailed donor information
        if (caisseEmprunt.getDonor() != null) {
            dto.setDonorId(caisseEmprunt.getDonor().getId());
            dto.setDonorName(getDonorDisplayName(caisseEmprunt.getDonor()));
        }

        // Add complete history
        List<CaisseEmpruntHistory> histories = caisseEmpruntHistoryRepository.findByCaisseEmpruntIdOrderByCreationDateDesc(id);
        List<CaisseEmpruntHistoryDto> historyDtos = histories.stream()
                .map(caisseEmpruntHistoryMapper::toDto)
                .collect(Collectors.toList());
        dto.setHistories(historyDtos);

        // Calculate summary information
        calculateSummaryInfo(dto, histories);

        log.info("Successfully retrieved detailed CaisseEmprunt with ID: {}", id);
        return dto;
    }

    private void calculateSummaryInfo(CaisseEmpruntDetailDto dto, List<CaisseEmpruntHistory> histories) {
        double totalEmprunts = histories.stream()
                .filter(h -> "EMPRUNT".equals(h.getType()))
                .mapToDouble(CaisseEmpruntHistory::getAmount)
                .sum();

        double totalRemboursements = histories.stream()
                .filter(h -> "REMBOURSEMENT".equals(h.getType()))
                .mapToDouble(CaisseEmpruntHistory::getAmount)
                .sum();

        dto.setTotalEmpruntAmount(totalEmprunts);
        dto.setTotalRemboursementAmount(totalRemboursements);
        dto.setRemainingAmount(totalEmprunts - totalRemboursements);
        dto.setTotalHistoryRecords(histories.size());
    }

    private void calculateSummaryInfoForList(CaisseEmpruntListDto dto, List<CaisseEmpruntHistory> histories) {
        double totalEmprunts = histories.stream()
                .filter(h -> "EMPRUNT".equals(h.getType()) )
                .mapToDouble(CaisseEmpruntHistory::getAmount)
                .sum();

        double totalRemboursements = histories.stream()
                .filter(h -> "REMBOURSEMENT".equals(h.getType()))
                .mapToDouble(CaisseEmpruntHistory::getAmount)
                .sum();

        dto.setTotalEmpruntAmount(totalEmprunts);
        dto.setTotalRemboursementAmount(totalRemboursements);
        dto.setRemainingAmount(totalEmprunts - totalRemboursements);
        dto.setTotalHistoryRecords(histories.size());
    }

    public Page<CaisseEmpruntListDto> getAllCaisseEmprunt(int page, int size, String searchByDonorName,
                                                          String searchByStatus, LocalDateTime searchByDateEmprunt,
                                                          LocalDateTime searchByDateRemboursement) {
        log.info("Getting all CaisseEmprunt with page: {}, size: {}, searchByDonorName: {}, searchByStatus: {}, searchByDateEmprunt: {}, searchByDateRemboursement: {}",
                page, size, searchByDonorName, searchByStatus, searchByDateEmprunt, searchByDateRemboursement);

        Pageable pageable = org.springframework.data.domain.PageRequest.of(page, size);
        Page<CaisseEmprunt> caisseEmpruntPage;

        if (searchByDonorName != null || searchByStatus != null || searchByDateEmprunt != null || searchByDateRemboursement != null) {
            caisseEmpruntPage = filterCaisseEmprunt(searchByDonorName, searchByStatus, searchByDateEmprunt, searchByDateRemboursement, pageable);
        } else {
            caisseEmpruntPage = caisseEmpruntRepository.findAllOrderByUpdateDateDesc(pageable);
        }

        List<CaisseEmpruntListDto> caisseEmpruntListDtos = caisseEmpruntPage.getContent().stream()
                .map(caisseEmprunt -> {
                    CaisseEmpruntListDto dto = caisseEmpruntMapper.toListDto(caisseEmprunt);
                    // Set donor name based on donor type
                    if (caisseEmprunt.getDonor() != null) {
                        dto.setDonorName(getDonorDisplayName(caisseEmprunt.getDonor()));
                    }

                    // Calculate summary information from histories
                    List<CaisseEmpruntHistory> histories = caisseEmpruntHistoryRepository.findByCaisseEmpruntIdOrderByCreationDateDesc(caisseEmprunt.getId());
                    calculateSummaryInfoForList(dto, histories);

                    return dto;
                })
                .collect(Collectors.toList());

        return new PageImpl<>(caisseEmpruntListDtos, pageable, caisseEmpruntPage.getTotalElements());
    }

    private Page<CaisseEmprunt> filterCaisseEmprunt(String searchByDonorName, String searchByStatus,
                                                   LocalDateTime searchByDateEmprunt, LocalDateTime searchByDateRemboursement,
                                                   Pageable pageable) {
        log.debug("Start service filterCaisseEmprunt with searchByDonorName: {}, searchByStatus: {}, searchByDateEmprunt: {}, searchByDateRemboursement: {}",
                searchByDonorName, searchByStatus, searchByDateEmprunt, searchByDateRemboursement);

        CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
        CriteriaQuery<CaisseEmprunt> criteriaQuery = criteriaBuilder.createQuery(CaisseEmprunt.class);
        Root<CaisseEmprunt> root = criteriaQuery.from(CaisseEmprunt.class);

        Predicate predicate = buildPredicate(criteriaBuilder, root, searchByDonorName, searchByStatus, searchByDateEmprunt, searchByDateRemboursement);
        criteriaQuery.where(predicate);
        criteriaQuery.orderBy(criteriaBuilder.desc(root.get("updateDate")));

        TypedQuery<CaisseEmprunt> typedQuery = entityManager.createQuery(criteriaQuery);
        long totalCount = typedQuery.getResultList().size();
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<CaisseEmprunt> resultList = typedQuery.getResultList();

        log.debug("End service filterCaisseEmprunt with {} CaisseEmprunt found", totalCount);
        return new PageImpl<>(resultList, pageable, totalCount);
    }
    private void addDonation(CaisseEmprunt caisseEmprunt) throws TechnicalException, IOException {
        log.info("Adding donation for CaisseEmprunt: {}", caisseEmprunt);

        // Create a new DonationDTO from CaisseEmprunt
        DonationDTO donationDTO = DonationDTO
                .builder()
                .value(caisseEmprunt.getGlobalAmount())
                .type("Financière")
                .enableCurrency(false)
                .canalDonation(CanalDonationDTO.builder().id(6L).build())

                .donor(DonorDTO.builder().id(caisseEmprunt.getDonor().getId()).build())
                .receptionDate(convertToDate(caisseEmprunt.getLastDateEmprunt()))
                .identifiedDonor(true)
                .build();
         donationDTO.setNonIdentifiedStatus("DISPONIBLE");
         donationDTO.setNonIdentifiedValue(caisseEmprunt.getGlobalAmount());

        // Save the donation using DonationService
        donationService.addDonation(donationDTO);
    }
    private Predicate buildPredicate(CriteriaBuilder criteriaBuilder, Root<CaisseEmprunt> root,
                                   String searchByDonorName, String searchByStatus,
                                   LocalDateTime searchByDateEmprunt, LocalDateTime searchByDateRemboursement) {
        Predicate predicate = criteriaBuilder.conjunction();

        // Filter by donor name - handle different donor types (following DonationService pattern)
        if (searchByDonorName != null && !searchByDonorName.trim().isEmpty()) {
            Join<CaisseEmprunt, Donor> donorJoin = root.join("donor", JoinType.LEFT);

            // Search across all donor types using OR condition (similar to DonationService approach)
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(
                    // Search in physical donor fields (firstName, lastName)
                    criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("firstName")), "%" + searchByDonorName.toLowerCase() + "%"),
                    criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("lastName")), "%" + searchByDonorName.toLowerCase() + "%"),
                    // Search in moral donor field (company)
                    criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("company")), "%" + searchByDonorName.toLowerCase() + "%"),
                    // Search in anonymous donor field (name)
                    criteriaBuilder.like(criteriaBuilder.lower(donorJoin.get("name")), "%" + searchByDonorName.toLowerCase() + "%")
            ));
        }

        // Filter by status
        if (searchByStatus != null && !searchByStatus.trim().isEmpty()) {
            predicate = criteriaBuilder.and(predicate,
                criteriaBuilder.like(criteriaBuilder.lower(root.get("status")),
                    "%" + searchByStatus.toLowerCase() + "%"));
        }

        // Filter by last date emprunt
        if (searchByDateEmprunt != null) {
            predicate = criteriaBuilder.and(predicate,
                criteriaBuilder.equal(
                    criteriaBuilder.function("DATE", LocalDateTime.class, root.get("lastDateEmprunt")),
                    criteriaBuilder.function("DATE", LocalDateTime.class, criteriaBuilder.literal(searchByDateEmprunt))
                ));
        }

        // Filter by last date remboursement
        if (searchByDateRemboursement != null) {
            predicate = criteriaBuilder.and(predicate,
                criteriaBuilder.equal(
                    criteriaBuilder.function("DATE", LocalDateTime.class, root.get("lastDateRemboursement")),
                    criteriaBuilder.function("DATE", LocalDateTime.class, criteriaBuilder.literal(searchByDateRemboursement))
                ));
        }

        return predicate;
    }

    /**
     * Get the appropriate display name for a donor based on its type (following DonationService pattern)
     */
    private String getDonorDisplayName(Donor donor) {
        if (donor instanceof DonorPhysical donorPhysical) {
            return donorPhysical.getFirstName() + " " + donorPhysical.getLastName();
        } else if (donor instanceof DonorMoral donorMoral) {
            return donorMoral.getCompany();
        } else if (donor instanceof DonorAnonyme donorAnonyme) {
            return donorAnonyme.getName();
        }
        // Fallback to label if type is not recognized
        return donor.getLabel() != null ? donor.getLabel() : "";
    }

}

import React, { useEffect, useRef, useState } from 'react';
import { Field, FieldArray, Form, Formik } from 'formik';
import * as Yup from 'yup';
import CanalDonations from 'containers/Donor/SubComponents/CanalDonations';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { useDispatch, useSelector } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import TypeProductNatures from 'containers/Common/SubComponents/TypeProductNature';
import Currencies from 'containers/Common/SubComponents/Currencies';
import { CustomSelect } from 'containers/Common/CustomInputs/CustomSelect';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import ChooseMultiDonors from 'containers/Common/SubComponents/ChooseMultiDonors';
import { Alert, Modal } from 'react-bootstrap';
import moment from 'moment';
import styles from '../../../Css/form.css';
import { addDonation, loadDonation, resetDonation, loadServiceCollectEps } from './actions';
import {
  makeSelectDonation,
  makeSelectError,
  makeSelectLoading,
  makeSelectSuccess,
  makeSelectActiveServiceCollectEps
} from './selectors';
import { makeSelectDonors } from '../../Donor/Donors/selectors';
import { CustomTextInput } from '../../Common/CustomInputs/CustomTextInput';
import donationReducer from './reducer';
import donationSaga from './saga';
import { makeSelectActiveServices } from 'containers/Service/ListService/selectors';
import serviceReducer from 'containers/Service/ListService/reducer';
import serviceSaga from 'containers/Service/ListService/saga';
import { loadActiveServicesByCategory } from 'containers/Service/ListService/actions';
import { makeSelectLoading as selectLoad } from 'containers/Service/ListService/selectors'
import ServiceCategories from 'containers/Common/SubComponents/ServiceCategories';
import { CustomTextArea } from 'containers/Common/CustomInputs/CustomTextArea';
import { CircularProgress } from '@mui/material';
import uploadFileIcone from 'images/icons/upload.svg';
import TagSelector from 'containers/Common/SubComponents/TagSelector';
import { makeSelectTagList } from 'containers/tag/selectors';
import { getTagsByType } from 'containers/tag/actions';
import tagReducer from 'containers/tag/reducer';
import tagSaga from 'containers/tag/saga';
const keyDonationAdd = 'donationAdd';
const key2 = 'serviceList';
const keyTag = 'tagList';
const formatDate = 'YYYY-MM-DD';

const omdbSelector = createStructuredSelector({
  donation: makeSelectDonation,
  donors: makeSelectDonors,
  success: makeSelectSuccess,
  error: makeSelectError,
  loading: makeSelectLoading,
  activeServiceCollectEps: makeSelectActiveServiceCollectEps,
  tagList: makeSelectTagList,
});

const omdbSelector2 = createStructuredSelector({
  activeServices: makeSelectActiveServices,
  loading2: selectLoad,
});

const backgroundStyle = {
  backgroundColor: 'white',
  border: '2px solid white ',
  borderRadius: '10px',
};

export default function AddDonation(globalProps) {
  useInjectReducer({
    key: keyDonationAdd,
    reducer: donationReducer,
  });
  useInjectSaga({ key: keyDonationAdd, saga: donationSaga });

  useInjectReducer({
    key: key2,
    reducer: serviceReducer,
  });
  useInjectSaga({ key: key2, saga: serviceSaga });

  useInjectReducer({
    key: keyTag,
    reducer: tagReducer,
  });
  useInjectSaga({ key: keyTag, saga: tagSaga });

  const dispatch = useDispatch();
  const { success, donation, loading, activeServiceCollectEps } = useSelector(omdbSelector);
  const { activeServices, loading2 } = useSelector(omdbSelector2);
  const formikRef = useRef();
  const [isInitialized, setIsInitialized] = useState(false);
  const history = useHistory();
  const params = useParams();
  const location = useLocation();
  const [showAlert, setShowAlert] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showBudgetLineFields, setShowBudgetLineFields] = useState(false);
  const [ErrorMessage, setErrorMessage] = useState('');
  const [
    hasExecutedOrReservedBudgetLine,
    setHasExecutedOrReservedBudgetLine,
  ] = useState(false);
  const [servicesByCategory, setServicesByCategory] = useState({});
  const [lastFetchedCategory, setLastFetchedCategory] = useState(null);
  const [serviceCollectEps, setServiceCollectEps] = useState([]);
  const [file, setFile] = useState(null);
  const [messageAddedDonation, setMessageAddedDonation] = useState('');
  const [selectedDonors, setSelectedDonors] = useState([]);

  const handleDonorsSelect = donors => {
    setSelectedDonors(donors);
    if (donors.length > 0 && formikRef.current) {
      formikRef.current.setFieldValue('donor.id', donors[0].id);

      // Update montant après déduction when donor is selected
      const currentValue = parseFloat(formikRef.current.values.value) || 0;
      const empruntSold = donors[0].empruntSold || 0;
      const montantApresDeduction = Math.max(currentValue - empruntSold, 0);
      formikRef.current.setFieldValue('montantApresDeduction', montantApresDeduction.toFixed(2));
    }
  };

  const SelectCategoryHandler = (value, index) => {
    const categoryId = value.id;
    // Fetch services for the selected category if not already fetched
    if (!servicesByCategory[categoryId]) {
      setLastFetchedCategory(categoryId);
      dispatch(loadActiveServicesByCategory(categoryId));
    }
  };

  useEffect(() => {
    console.log('activeServiceCollectEps', activeServiceCollectEps)
    if (!activeServiceCollectEps || activeServiceCollectEps.length === 0) {
      dispatch(loadServiceCollectEps());
    }
  }, [dispatch]);

  // Load tags for the tag selector
  useEffect(() => {
    dispatch(getTagsByType('donation'));
  }, [dispatch]);

  useEffect(() => {
    if (activeServices && lastFetchedCategory) {
      // Merge new services with existing ones for the fetched category
      setServicesByCategory(prev => ({
        ...prev,
        [lastFetchedCategory]: activeServices,
      }));
      setLastFetchedCategory(null);
    }
  }, [activeServices, lastFetchedCategory]);
  useEffect(() => {
    if (donation) {
      const sanitizedDonation = JSON.parse(
        JSON.stringify(donation, (key, value) =>
          value === null ? undefined : value,
        ), // Remove null values
      );

      if (
        sanitizedDonation.budgetLines &&
        sanitizedDonation.budgetLines.length > 0
      ) {
        sanitizedDonation.budgetLines.forEach((line, index) => {
          if (line.service && line.service.serviceCategoryId) {
            SelectCategoryHandler(
              { id: line.service.serviceCategoryId },
              index,
            );
          }
        })
      }
    }
  }, [donation, loading, loading2])
  useEffect(() => {
    if (donation) {
      const sanitizedDonation = JSON.parse(
        JSON.stringify(donation, (key, value) =>
          value === null ? undefined : value,
        ), // Remove null values
      );

      console.log('sanitizedDonation', sanitizedDonation)

      const receptionDate = moment(donation.receptionDate).format(formatDate);

      // Initialize the donor selection when loading a donation
      if (sanitizedDonation.donor) {
        const donor = sanitizedDonation.donor;
        setSelectedDonors([{
          id: donor.id,
          name: donor.firstName ? `${donor.firstName} ${donor.lastName}` : donor.company || donor.name,
          type: donor.type,
          picture64: donor.picture64
        }]);
      }

      if (
        sanitizedDonation.budgetLines &&
        sanitizedDonation.budgetLines.length > 0
      ) {
        setShowBudgetLineFields(true);
        sanitizedDonation.budgetLines.forEach((line, index) => {
          if (line.service && line.service.serviceCategoryId) {
            SelectCategoryHandler(
              { id: line.service.serviceCategoryId },
              index,
            );
          }
          if (line.service && line.service.id) {
            formikRef.current.setFieldValue(
              `budgetLines[${index}].service.id`,
              line.service.id,
            );
          }
          if (line.service && line.serviceCollectEpsId) {

            const service = activeServices && activeServices.find(s => s.id === line.service.id);
            if (service && service.consultServiceCollectEpsDTOS) {
              setServiceCollectEps(prev => [
                ...prev,
                { id: index, data: service.consultServiceCollectEpsDTOS },
              ]);
            }

            console.log(`budgetLines[${index}].serviceCollectEpsId`, line.serviceCollectEpsId)

            formikRef.current.setFieldValue(
              `budgetLines[${index}].serviceCollectEpsId`,
              line.serviceCollectEpsId,
            );

          }
        });

        const hasExecutedOrReserved = sanitizedDonation.budgetLines.some(
          line => line.status === 'EXECUTED' || line.status === 'RESERVED',
        );
        setHasExecutedOrReservedBudgetLine(hasExecutedOrReserved);
      }
      const formBody = {
        ...formikRef.current.values,
        ...sanitizedDonation,
        receptionDate,
      };
      formikRef.current.setValues(formBody);
    }
  }, [donation, loading]);

  useEffect(() => {
    if (globalProps.donation) {

      const removeCircular = obj => {
        const seen = new WeakSet();
        return JSON.parse(
          JSON.stringify(obj, (key, value) => {
            if (typeof value === 'object' && value !== null) {
              if (seen.has(value)) {
                return;
              }
              seen.add(value);
            }
            return value;
          }),
        );
      };

      const donationToEdit = removeCircular(globalProps.donation);
      const receptionDate = moment(globalProps.donation.receptionDate).format(
        formatDate,
      );
      const formBody = {
        ...formikRef.current.values,
        ...donationToEdit,
        receptionDate,
        enableCurrency,
      };

      formikRef.current.setValues(formBody);
    }
  }, [globalProps.donation]);

  useEffect(() => {
    if (location.state && !isInitialized) {
      const { donor } = location.state;
      if (donor && formikRef.current) {
        formikRef.current.setFieldValue('donor', donor);
        setSelectedDonors([{
          id: donor.id,
          name: donor.firstName ? `${donor.firstName} ${donor.lastName}` : donor.company || donor.name,
          type: donor.type,
          picture64: donor.picture64
        }]);
      }
    }

    if (globalProps && globalProps.donor && !isInitialized) {
      if (formikRef.current) {
        formikRef.current.setFieldValue('donor', globalProps.donor);
        setSelectedDonors([{
          id: globalProps.donor.id,
          name: globalProps.donor.firstName ? `${globalProps.donor.firstName} ${globalProps.donor.lastName}` : globalProps.donor.company || globalProps.donor.name,
          type: globalProps.donor.type,
          picture64: globalProps.donor.picture64
        }]);
      }
    }
  }, [location.state, globalProps, isInitialized]);

  useEffect(() => {
    if (showBudgetLineFields) {
      formikRef.current.setFieldValue('withbudgetLines', true);
    } else {
      formikRef.current.setFieldValue('withbudgetLines', false);
    }
  }, [showBudgetLineFields]);

  useEffect(
    () =>
      function cleanup() {
        dispatch(resetDonation());
      },
    [],
  );

  useEffect(() => {
    if (params.idDonation) {
      formikRef.current.resetForm();
      dispatch(loadDonation(params.idDonation));

    }
  }, [params.idDonation]);

  useEffect(() => {
    if (!params.idDonation) {
      formikRef.current.resetForm();
      dispatch(resetDonation());
    }
  }, [params.idDonation]);

  let canalDonationName = null;
  useEffect(() => {
    if (success === true) {
      if (globalProps.profile) {
        globalProps.handleClose();
      } else if (params.idDonation) {
        const successMessage = params.idDonation
          ? 'Donation modifié avec succès !'
          : 'Donation ajouté avec succès !';

        setShowAlert(true);
        setMessageAddedDonation(
          <Alert
            className="pb-0"
            variant="success"
            onClose={() => setShowAlert(false)}
            dismissible
          >
            <p>{successMessage}</p>
          </Alert>,
        );

        if (location.state && location.state.redirectTo) {
          history.push({
            pathname: `/donations/fiche/${params.idDonation}/info`,
            state: 'updateSuccess',
          });
        } else {
          history.push({
            pathname: '/donations',
            state: params.idDonation ? 'updateSuccess' : 'success',
            donation: donation ? donation : null,
          });
        }
      } else {
        history.push({
          pathname: '/donations',
          state: 'success',
          donation: donation ? donation : null,
        });
      }
      dispatch(resetDonation());
    }
  }, [
    success,
    params.idDonation,
    dispatch,
    globalProps,
    history,
    location.state,
  ]);

  useEffect(() => {
    if (ErrorMessage) {
      setShowAlert(true);
      setMessageAddedDonation(
        <Alert
          className="pb-0"
          variant="danger"
          onClose={() => setErrorMessage('')}
          dismissible
        >
          <p>{ErrorMessage}</p>
        </Alert>,
      );
    }
  }, [ErrorMessage]);

  const onSelectHandler = canal => {
    canalDonationName = canal.name;
    if (formikRef.current) {
      formikRef.current.setFieldValue('canalDonation.id', canal.id);
    }
  };

  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10 MB

  const DonationTypeRadioGroup = ({ disabledNature, disabledFinancial }) => (
    <div className="d-flex justify-content-center m-3 pb-3">
      <div className="mr-3">
        <h5 className="">Type donation :</h5>
      </div>
      <div className="custom-control custom-radio custom-control-inline">
        <Field
          type="radio"
          id="customRadioInline1"
          name="type"
          value="Financière"
          className="custom-control-input"
          onClick={() => {
            // we shoudl empty the table of the budget lines
            formikRef.current.setFieldValue('budgetLines', []);
            formikRef.current.setFieldValue('value', '');
            // the file also should be empty
            setFile(null);
            formikRef.current.setFieldValue('file', null);
            formikRef.current.setFieldValue('fileName', '');
            // and then we can add the first row of the budget lines
            setShowBudgetLineFields(true);

            formikRef.current.setFieldValue('budgetLines', [
              {
                amount: '',
                amountByBeneficiary: '',
                service: {
                  id: '',
                  serviceCategoryId: '',
                  serviceCollectEps: '',
                  propositionSystem: false,
                },
                comment: '',
                status: 'DISPONIBLE',
                type: '',
                makeItAvailable: false,
                natureBudgetLine: false,
                productCategory: [],
              },
            ]);
          }}
          disabled={disabledFinancial}
        />
        <label className="custom-control-label" htmlFor="customRadioInline1">
          Financière
        </label>
      </div>
      <div className="custom-control custom-radio custom-control-inline">
        <Field
          type="radio"
          id="customRadioInline2"
          name="type"
          value="Nature"
          className="custom-control-input"
          disabled={disabledNature}
          onClick={() => {
            // we shoudl empty the table of the budget lines
            formikRef.current.setFieldValue('budgetLines', []);
            formikRef.current.setFieldValue('value', '');
            formikRef.current.setFieldValue('valueCurrency', '');
            formikRef.current.setFieldValue('currency.id', '');
            formikRef.current.setFieldValue('enableCurrency', false);
            formikRef.current.setFieldValue('nonIdentifiedValue', '');
            formikRef.current.setFieldValue('canalDonation.id', '');
            setShowBudgetLineFields(true);
            formikRef.current.setFieldValue('budgetLines', [
              {
                amount: '',
                amountByBeneficiary: '',
                service: {
                  id: '',
                  serviceCategoryId: '',
                  propositionSystem: false,
                },
                comment: '',
                status: 'DISPONIBLE',
                type: '',
                makeItAvailable: false,
                natureBudgetLine: true,
                productCategory: [],
              },
            ]);
          }}
        />
        <label className="custom-control-label" htmlFor="customRadioInline2">
          En nature
        </label>
      </div>
    </div>
  );

  const getStatusConfig = status => {
    const statusMap = {
      RESERVED: {
        label: 'Réservée',
        color: '#fff3e0',
        textColor: '#ef6c00',
        disabled: true,
        className: 'badge-warning',
      },
      EXECUTED: {
        label: 'Exécutée',
        color: '#e1f5fe',
        textColor: '#0277bd',
        disabled: true,
        className: 'badge-primary',
      },
      REMAINING: {
        label: 'Restante',
        color: '#c8e6c9',
        textColor: '#2e7d32',
        disabled: true,
        className: 'badge-danger',
      },
    };
    return statusMap[status] || {};
  };

  const [originalValues, setOriginalValues] = useState({});

  const isUpdating = params.idDonation ? true : false;
  return (
    <div className="mr-5 ml-5 p-3" style={backgroundStyle}>
      {messageAddedDonation}
      <Formik
        initialValues={{
          canalDonation: {
            id: '',
          },
          value: '',
          comment: '',
          type: 'Financière',
          selectedDonorType: '',
          donor: {},
          firstName: '',
          lastName: '',
          company: '',
          unknownDonor: false,
          enableCurrency: false,
          valueCurrency: '',
          currency: {
            id: '',
          },
          transactionNumber: '',
          receptionDate: moment().format(formatDate),
          unidentifiedDonorName: '',
          budgetLines: [],
          withbudgetLines: false,
          NonIdentifiedDonation: false,
          nonIdentifiedValue: '',
          nonIdentifiedComment: '',
          nonIdentifiedStatus: 'DISPONIBLE',
          isUpdating: isUpdating,
          documents: [],
          tags: [],
          montantApresDeduction: '0.00',
        }}
        onSubmit={(values, { setSubmitting, resetForm }) => {
          setSubmitting(true);
          setIsLoading(true);
          const canalDonation = {
            ...values.canalDonation,
            name: canalDonationName,
          };
          const body = {
            ...values,
            donor: values.donor.id ? { id: values.donor.id } : null,
            budgetLines: values.budgetLines,
            identifiedDonor: !values.unknownDonor,
            canalDonation,
            withbudgetLines: values.withbudgetLines,
            NonIdentifiedDonation: values.NonIdentifiedDonation,
            idNonIdentifed: values.idNonIdentifed,
            idKafalat: values.idKafalat,
            listProduct: [],
            documentDonations: [],
            tags: values.tags,
          };

          dispatch(addDonation(body));
          setSubmitting(false);
          setIsLoading(false);
        }}
        validationSchema={Yup.object({
          canalDonation: Yup.mixed().when('type', {
            is: 'Financière',
            then: Yup.object().shape({
              id: Yup.number().required('Canal de donation est requis'),
            }),
          }),

          isUpdating: Yup.boolean(),
          documents: Yup.array()
            .test('documentsRequired', 'Aucun document ajouté', function (
              documents,
            ) {
              if (documents && documents.length === 0) {
                return true; // No documents, no validation required
              }
              return documents.every(doc => doc.file && doc.label); // Validate 'file' and 'object' if there are documents
            })
            .of(
              Yup.object().shape({
                file: Yup.mixed()
                  .required('Le fichier est requis')
                  .nullable(),
                fileName: Yup.string(),
                fileUrl: Yup.string(),
                file64: Yup.string(),
                type: Yup.string(),
                label: Yup.string().required("L'objet est requis"),
                description: Yup.string(),
                expiryDate: Yup.date()
                  .nullable()
                  .test(
                    'is-valid-date',
                    "La date d'expiration doit être supérieure à la date actuelle",
                    function (value) {
                      return !value || moment(value).isAfter(moment());
                    },
                  ),

                yup: Yup.string(),
              }),
            ),

          receptionDate: Yup.date().required('Date de réception est requise'),

          donor: Yup.mixed().when('unknownDonor', {
            is: false,
            then: Yup.object().shape({
              id: Yup.string().required(
                'Donateur est requis, veuillez sélectionner un donateur',
              ),
            }),
          }),
          valueCurrency: Yup.mixed().when('enableCurrency', {
            is: true,
            then: Yup.number().required('Equivalent est requis'),
          }),
          currency: Yup.mixed().when('enableCurrency', {
            is: true,
            then: Yup.object().shape({
              id: Yup.number().required('Devise est requise'),
            }),
          }),
          budgetLines: Yup.array().when('withbudgetLines', {
            is: withbudgetLines => withbudgetLines,
            then: Yup.array().of(
              Yup.object().shape({
                amount: Yup.number()
                  .required('Montant est requis')
                  .moreThan(0, 'Le montant doit être supérieur à 0')
                  .typeError('Le montant doit être un nombre'),

                amountByBeneficiary: Yup.number()
                  .typeError('Montant par bénéficiaire doit être un nombre')
                  .when('amount', (amount, schema) =>
                    amount > 0
                      ? schema
                        .moreThan(
                          0,
                          'Montant par bénéficiaire doit être supérieur à 0',
                        )
                        .max(
                          Yup.ref('amount'),
                          'Montant par bénéficiaire doit être inférieur ou égal au montant donné',
                        )
                      : schema,
                  ),
                service: Yup.object().shape({
                  id: Yup.string().required('Le service est requis'),
                  serviceCategoryId: Yup.number().required(
                    'Catégorie est requise',
                  ),
                  // serviceCollectEpsId: Yup.number().required(
                  //   'Service Collect Eps est requise',
                  // ),
                }),

                natureBudgetLine: Yup.boolean().default(false), // Ensure default value

                productCategory: Yup.array()
                  .of(Yup.number().required('Catégorie de produit est requise')) // Accepts an array of numbers
                  .when('natureBudgetLine', {
                    is: true,
                    then: schema =>
                      schema.min(
                        1,
                        'Au moins une catégorie de produit est requise',
                      ),
                  }),
              }),
            ),
          }),

          value: Yup.number()
            .required('Montant Total est requis')
            .moreThan(0, 'Montant Total doit être supérieur à 0')
            .typeError('Montant Total doit être un nombre')
            .test(
              'is-valid-total',
              'Le montant total doit être égal à la somme des montants',
              function (value) {
                const { nonIdentifiedValue, budgetLines } = this.parent;

                const isNatureDonation =
                  budgetLines[0] && budgetLines[0].natureBudgetLine === true;

                // Calculate total amount from budget lines
                const totalBudgetLines = budgetLines.reduce(
                  (sum, line) => sum + (+line.amount || 0),
                  0,
                );

                // Expected total calculation
                const expectedTotal = isNatureDonation
                  ? totalBudgetLines // Ignore nonIdentifiedValue for nature donations
                  : (nonIdentifiedValue || 0) + totalBudgetLines;

                return value === expectedTotal;
              },
            ),
        })}
        innerRef={formikRef}
      >
        {props => (
          <Form onkeydown="return event.key != 'Enter';">
            <Modal
              show={loading || loading2}
              centered
              contentClassName="bg-transparent border-0"
            >
              <div className="d-flex justify-content-center align-items-center">
                <CircularProgress style={{ width: '100px', height: '100px' }} />
              </div>
            </Modal>
            {globalProps.profile ? null : (
              <h3 className="m-2">
                {params.idDonation
                  ? 'Modifier cette donation'
                  : 'Ajouter une donation'}
              </h3>
            )}

            <div className="d-flex justify-content-center mt-5">
              {params.idDonation ? (
                <DonationTypeRadioGroup
                  isFinancial={props.values.type === 'Financière'}
                  disabledNature={props.values.type === 'Financière'}
                  disabledFinancial={props.values.type !== 'Financière'}
                  disabledUnidentified={
                    props.values.type === 'Financière' ||
                    props.values.type === 'Nature'
                  }
                />
              ) : (
                <DonationTypeRadioGroup
                  disabledNature={false}
                  disabledFinancial={false}
                  disabledUnidentified={false}
                />
              )}
            </div>

            {globalProps.profile ? null : (
              <div
                className="d-flex align-items-center flex-column"
                style={{ alignItems: 'flex-start' }}
              >
                <ChooseMultiDonors
                  onDonorsSelect={handleDonorsSelect}
                  getedSelectedDonors={selectedDonors}
                  props={props}
                  isTouched
                  disabled={hasExecutedOrReservedBudgetLine}
                  type="donation"
                />
              </div>
            )}

            <div
              style={{
                maxWidth: '1000px',
                margin: '0 auto',
                padding: '1rem',
              }}
            >
              <div
                className="d-flex justify-content-between align-items-center mb-4"
                style={{ width: '100%', borderBottom: '1px solid #32cd32' }}
              >
                <h3
                  className="font-weight-bold"
                  style={{
                    fontSize: '1.25rem',
                    marginTop: '2rem',
                  }}
                >
                  Informations Générales
                </h3>
              </div>
              <div className="d-flex justify-content-between mb-3">
                <div
                  className="form-group col-md-5 pl-0"
                  style={{ marginRight: '1rem' }}
                >
                  <CustomTextInput
                    label="Date de Réception"
                    type="date"
                    isRequired
                    name="receptionDate"
                    formProps={props}
                  />
                </div>

                {props.values.type === 'Financière' && (
                  <div className="form-group col-md-5 pl-0">
                    <CanalDonations
                      label="Canal de donation"
                      isRequired
                      name="canalDonation.id"
                      onSelect={onSelectHandler}
                      style={{ width: '100%' }}
                    />
                  </div>
                )}
              </div>

              <div className="mb-4">
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <div
                    className="form-group col-md-5 pl-0"
                    style={{ marginRight: '1rem' }}
                  >
                    <CustomTextInput
                      label="Montant Total (DH)"
                      isRequired
                      name="value"
                      placeholder="500.00"
                      type="text"
                      formProps={props}
                      style={{ width: '100%' }}
                      onChange={e => {
                        const newValue = parseFloat(e.target.value) || 0;
                        props.setFieldValue('value', newValue);

                        const totalBudgetLines = props.values.budgetLines.reduce(
                          (sum, line) => sum + (+line.amount || 0),
                          0,
                        );

                        const nonIdentifiedValue = newValue - totalBudgetLines;
                        props.setFieldValue(
                          'nonIdentifiedValue',
                          Math.max(nonIdentifiedValue, 0),
                        );

                        // Update montant après déduction
                        const empruntSold = selectedDonors && selectedDonors.length > 0 ? (selectedDonors[0].empruntSold || 0) : 0;
                        const montantApresDeduction = Math.max(newValue - empruntSold, 0);
                        props.setFieldValue('montantApresDeduction', montantApresDeduction.toFixed(2));
                      }}
                      // in the case of nature it should be disabled and also he should have the same value of the total
                      disabled={props.values.type === 'Nature'}
                    // he should have automaticly the total valus of the budget lines
                    />
                  </div>

                  <div className="form-group col-md-5 pl-0">
                    <CustomTextInput
                      label="Numéro de reçus"
                      name="transactionNumber"
                      placeholder="Numéro de reçus"
                      style={{ width: '100%' }}
                    />
                  </div>
                </div>

                {/* Montant après déduction field */}
                {selectedDonors && selectedDonors.length > 0 && selectedDonors[0].empruntSold > 0 && (
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <div
                      className="form-group col-md-5 pl-0"
                      style={{ marginRight: '1rem' }}
                    >
                      <CustomTextInput
                        label="Montant après déduction (DH)"
                        name="montantApresDeduction"
                        type="text"
                        formProps={props}
                        disabled={true}
                        style={{
                          width: '100%',
                          backgroundColor: '#f8f9fa',
                          color: '#6c757d'
                        }}
                      />
                      <small className="text-info" style={{ fontSize: '12px', fontWeight: 'bold' }}>
                        💰 Montant après déduction du solde d'emprunt ({selectedDonors[0].empruntSold || 0} DH)
                      </small>
                    </div>
                    <div className="form-group col-md-5 pl-0">
                      {/* Empty div to maintain layout */}
                    </div>
                  </div>
                )}
                <div className="d-flex justify-content-between align-items-center mb-3">
                  <div className="form-group col-md-12 pl-0">
                    <TagSelector formProps={props} taggableType="donation" />
                  </div>
                </div>

                <div className="d-flex justify-content-between align-items-center mb-3">
                  <div className="form-group col-md-12 pl-0">
                    <CustomTextArea
                      label="Commentaire"
                      name="comment"
                      placeholder="Commentaire"
                      style={{ width: '100%' }}
                    />
                  </div>
                </div>

                {props.values.type === 'Financière' && (
                  <div
                    className="custom-control custom-switch"
                    style={{ marginRight: '1rem', marginBottom: '1rem' }}
                  >
                    <Field
                      type="checkbox"
                      name="enableCurrency"
                      className="custom-control-input"
                      id="customSwitchCurrency"
                    />
                    <label
                      className="custom-control-label"
                      htmlFor="customSwitchCurrency"
                    >
                      Montant avec diffèrente devise
                    </label>
                  </div>
                )}

                {props.values.enableCurrency && (
                  <div className="d-flex justify-content-between mb-3">
                    <div
                      className="form-group col-md-5 pl-0"
                      style={{ marginRight: '1rem' }}
                    >
                      <Currencies
                        label="Devise"
                        isRequired
                        name="currency.id"
                        formProps={props}
                      />
                    </div>
                    <div className="form-group col-md-5 pl-0">
                      <CustomTextInput
                        label="Equivalent"
                        isRequired
                        name="valueCurrency"
                        placeholder="50.00"
                        formProps={props}
                        type="text"
                        style={{ width: '100%' }}
                      />
                    </div>
                  </div>
                )}
              </div>

              {!isUpdating && (
                <FieldArray name="documents">
                  {({ insert, remove }) => (
                    <div>
                      <div
                        className="d-flex justify-content-between align-items-center mb-4"
                        style={{
                          width: '100%',
                          borderBottom: '1px solid gray',
                        }}
                      >
                        <h3
                          className="font-weight-bold"
                          style={{
                            fontSize: '1.25rem',
                            marginTop: '2rem',
                          }}
                        >
                          Documents de donation
                        </h3>

                        <button
                          type="button"
                          onClick={() =>
                            insert(0, {
                              file: null,
                              fileUrl: '',
                              fileName: "Aucun fichier n'est sélectionné",
                              file64: 'file64',
                              type: 'document',
                              label: '',
                              description: '',
                              expiryDate: '',
                              type: '',
                            })
                          }
                          className="btn-style primary"
                          style={{ fontSize: '1rem' }}
                        >
                          <span className="fa fa-plus"></span> Ajouter
                        </button>
                      </div>

                      {props.values.documents.map((document, index) => (
                        <div
                          key={index}
                          className="mb-4 p-3 border rounded"
                          style={{
                            border: '1px solid #4B8DA3',
                            borderRadius: '0.25rem',
                            padding: '1rem',
                            marginBottom: '1rem',
                            marginTop: '1rem',
                            backgroundColor: '#f0f0f0',
                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                          }}
                        >
                          <div className="d-flex justify-content-between align-items-center">
                            <div className="form-group col-md-5 pl-0">
                              <CustomTextInput
                                label="Objet"
                                name={`documents.${index}.label`}
                                placeholder="Objet"
                                style={{ width: '100%' }}
                                formProps={props}
                                isRequired
                              />
                            </div>
                            <div className="form-group col-md-5 pl-0">
                              <label
                                htmlFor={`documentUpload-${index}`}
                                className="mb-2"
                              >
                                Détail de la donation (Max 10MB)
                                <span className="text-danger">*</span>
                              </label>
                              <div className="d-flex align-items-center">
                                {!document.file ? (
                                  <label
                                    htmlFor={`documentUpload-${index}`}
                                    className="btn btn-outline-primary btn-sm d-flex align-items-center px-3"
                                    style={{ cursor: 'pointer' }}
                                  >
                                    <img
                                      src={uploadFileIcone}
                                      alt="Upload"
                                      width="20px"
                                      height="20px"
                                      className="mr-2"
                                    />
                                    Choisir un fichier
                                  </label>
                                ) : (
                                  <div className="d-flex align-items-center justify-content-between w-100 p-2 border rounded bg-light">
                                    <span className="text-success">
                                      {document.file.name}
                                    </span>
                                    <button
                                      type="button"
                                      className="btn btn-sm btn-danger"
                                      style={{ fontSize: '1rem',borderRadius: '35px'}}
                                      onClick={() => {
                                        props.setFieldValue(
                                          `documents.${index}.file`,
                                          null,
                                        );
                                        props.setFieldValue(
                                          `documents.${index}.fileName`,
                                          '',
                                        );
                                      }}
                                    >
                                      Supprimer
                                    </button>
                                  </div>
                                )}

                                <input
                                  type="file"
                                  name={`documents.${index}.file`}
                                  id={`documentUpload-${index}`}
                                  hidden
                                  onChange={event => {
                                    const selectedFile = event.target.files[0];
                                    if (selectedFile) {
                                      if (
                                        selectedFile.size >
                                        10 * 1024 * 1024
                                      ) {
                                        setErrorMessage(
                                          'Le fichier dépasse la taille maximale de 10 MB.',
                                        );
                                        props.setFieldValue(
                                          `documents.${index}.file`,
                                          null,
                                        );
                                      } else {
                                        setErrorMessage('');
                                        props.setFieldValue(
                                          `documents.${index}.file`,
                                          selectedFile,
                                        );
                                        props.setFieldValue(
                                          `documents.${index}.fileName`,
                                          selectedFile.name,
                                        );
                                      }
                                    }
                                  }}
                                />
                              </div>
                              {props.touched.documents &&
                                props.touched.documents[index] &&
                                props.touched.documents[index].file &&
                                props.errors.documents &&
                                props.errors.documents[index] &&
                                props.errors.documents[index].file && (
                                  <p className={`m-2 mb-0 ${styles.errorText}`}>
                                    {props.errors.documents[index].file}
                                  </p>
                                )}
                            </div>
                          </div>
                          <div className="d-flex justify-content-between align-items-center">
                            <div className="form-group col-md-5 pl-0">
                              <CustomTextInput
                                label="Date d'expiration"
                                type="Date"
                                name={`documents.${index}.expiryDate`}
                                placeholder="Date d'expiration"
                                style={{ width: '100%' }}
                              />
                            </div>
                            <div className="form-group col-md-5 pl-0">
                              <CustomTextArea
                                label="Description"
                                name={`documents.${index}.description`}
                                placeholder="Description"
                                style={{ width: '100%' }}
                              />
                            </div>
                          </div>
                          <button
                            type="button"
                            onClick={() => remove(index)}
                            className="btn btn-outline-danger"
                            style={{ fontSize: '1rem',borderRadius: '35px' }}
                          >
                            <span className="fa fa-trash"></span> Supprimer
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </FieldArray>
              )}

              <FieldArray name="budgetLines">
                {({ insert, remove, push }) => (
                  <div>
                    {/* Header Section */}
                    <div
                      className="d-flex justify-content-between align-items-center mb-4"
                      style={{
                        width: '100%',
                        borderBottom: '1px solid blue',
                      }}
                    >
                      <h3
                        className="font-weight-bold"
                        style={{
                          fontSize: '1.25rem',
                          marginTop: '2rem',
                        }}
                      >
                        Ligne de donation identifiée
                      </h3>
                      <button
                        type="button"
                        onClick={() => {
                          setShowBudgetLineFields(true);
                          insert(0, {
                            amount: '',
                            amountByBeneficiary: '',
                            service: {
                              id: '',
                              serviceCategoryId: '',
                              propositionSystem: false,
                            },
                            serviceCollectEpsId: '',
                            comment: '',
                            status: 'DISPONIBLE',
                            type: '',
                            makeItAvailable: false,
                            natureBudgetLine:
                              props.values.type === 'Nature' ? true : false,
                            productCategory: [],
                          });
                        }}
                        className="btn-style primary"
                        style={{ fontSize: '1rem' }}

                      >
                        <span className="fa fa-plus"></span> Ajouter
                      </button>
                    </div>
                    {(showBudgetLineFields ||
                      (props.values.budgetLines &&
                        props.values.budgetLines.length > 0 &&
                        (props.values.budgetLines[0].amount ||
                          props.values.budgetLines[0].service.id))) && (
                        <div>
                          {props.values.budgetLines.map((lineBudget, index) => {
                            const statusConfig = getStatusConfig(
                              lineBudget.status,
                            );

                            return (
                              <div
                                key={index}
                                className="mb-4 p-3 border rounded"
                                style={{
                                  border: '1px solid #4B8DA3', // Soft teal border for identified
                                  borderRadius: '0.25rem',
                                  padding: '1rem',
                                  marginBottom: '1rem',
                                  marginTop: '1rem',
                                  backgroundColor: '#f0f0f0',
                                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', // Subtle shadow for depth
                                }}
                              >
                                {statusConfig.label && (
                                  <div className="d-flex justify-content-between align-items-center">
                                    <div
                                      className={statusConfig.className}
                                      style={{
                                        padding: '0.25rem 0.5rem',
                                        borderRadius: '0.25rem',
                                        fontSize: '0.75rem',
                                        textTransform: 'uppercase',
                                        marginBottom: '1rem',
                                      }}
                                    >
                                      <span>{statusConfig.label}</span>
                                    </div>
                                    {lineBudget.status === 'REMAINING' && (
                                      <div className="d-flex align-items-center mb-3">
                                        {/* Checkbox Input */}
                                        <input
                                          id={`switch-${index}`}
                                          type="checkbox"
                                          checked={lineBudget.makeItAvailable}
                                          onChange={e => {
                                            const isChecked = e.target.checked;

                                            // Store original values when checked for the first time
                                            if (
                                              isChecked &&
                                              !originalValues[index]
                                            ) {
                                              const {
                                                serviceCategoryId,
                                                id,
                                              } = props.values.budgetLines[
                                                index
                                              ].service;

                                              setOriginalValues(prev => ({
                                                ...prev,
                                                [index]: {
                                                  oldServiceCategoryId: serviceCategoryId,
                                                  oldServiceId: id,
                                                },
                                              }));
                                            }
                                            // Restore original values when unchecked
                                            else if (
                                              !isChecked &&
                                              originalValues[index]
                                            ) {
                                              props.setFieldValue(
                                                `budgetLines.${index}.service.serviceCategoryId`,
                                                originalValues[index]
                                                  .oldServiceCategoryId,
                                              );
                                              props.setFieldValue(
                                                `budgetLines.${index}.service.id`,
                                                originalValues[index]
                                                  .oldServiceId,
                                              );
                                            }

                                            props.setFieldValue(
                                              `budgetLines.${index}.makeItAvailable`,
                                              isChecked,
                                            );
                                          }}
                                          className="form-check-input"
                                          style={{
                                            width: '1rem',
                                            height: '1rem',
                                            cursor: 'pointer',
                                          }}
                                        />

                                        {/* Label for the Checkbox */}
                                        <label
                                          htmlFor={`switch-${index}`}
                                          className="ml-2"
                                          style={{
                                            fontWeight: 'bold',
                                            marginTop: '0.7rem',
                                          }}
                                        >
                                          Rendre disponible
                                        </label>
                                      </div>
                                    )}
                                  </div>
                                )}

                                <div className="d-flex justify-content-between align-items-center mb-2">
                                  <div className="form-group col-md-5 pl-0">
                                    <ServiceCategories
                                      disabled={statusConfig.disabled}
                                      value={
                                        props.values.budgetLines[index] &&
                                          props.values.budgetLines[index].service &&
                                          props.values.budgetLines[index].service
                                            .serviceCategoryId
                                          ? props.values.budgetLines[index]
                                            .service.serviceCategoryId
                                          : ''
                                      }
                                      name={`budgetLines.${index}.service.serviceCategoryId`}
                                      onSelectCanal={function (value) {
                                        SelectCategoryHandler(value, index);

                                      }}
                                      label="Catégorie"
                                      isRequired
                                    />
                                  </div>

                                  <div className="form-group col-md-5 pl-0 mb-3">
                                    <CustomSelect
                                      disabled={statusConfig.disabled}
                                      label="Service"
                                      name={`budgetLines.${index}.service.id`}
                                      isRequired
                                      formProps={props}
                                      onChange={e => {
                                        const serviceId = e.target.value;
                                        const budgetLine =
                                          formikRef.current.values.budgetLines[
                                          index
                                          ];
                                        let selectedService = null;

                                        if (
                                          budgetLine &&
                                          budgetLine.service &&
                                          budgetLine.service.serviceCategoryId
                                        ) {
                                          const services =
                                            servicesByCategory[
                                            budgetLine.service.serviceCategoryId
                                            ] || [];
                                          selectedService = services.find(
                                            s => s.id.toString() === serviceId,
                                          );
                                        }

                                        if (selectedService) {
                                          if (selectedService.eps != null) {
                                            setServiceCollectEps(prevState => {
                                              const existingIndex = prevState.findIndex(item => item.id === index);

                                              if (existingIndex !== -1) {
                                                // If the ID exists, update the data
                                                return prevState.map(item =>
                                                  item.id === index ? { ...item, data: selectedService.consultServiceCollectEpsDTOS } : item
                                                );
                                              } else {
                                                // If the ID does not exist, add a new entry
                                                return [
                                                  ...prevState,
                                                  { id: index, data: selectedService.consultServiceCollectEpsDTOS }
                                                ];
                                              }
                                            });

                                          }
                                          else {
                                            setServiceCollectEps(prevState => {
                                              const existingIndex = prevState.findIndex(item => item.id === index);

                                              if (existingIndex !== -1) {
                                                // If the ID exists, update data to null
                                                return prevState.map(item =>
                                                  item.id === index ? { ...item, data: null } : item
                                                );
                                              } else {
                                                // If the ID does not exist, add a new entry with data: null
                                                return [...prevState, { id: index, data: null }];
                                              }
                                            });

                                          }



                                          // Populate other service fields when the service is selected
                                          props.setFieldValue(
                                            `budgetLines.${index}.service.propositionSystem`,
                                            selectedService.propositionSystem,
                                          );

                                          props.setFieldValue(
                                            `budgetLines.${index}.type`,
                                            selectedService.category || '',
                                          );
                                          props.setFieldValue(
                                            `budgetLines.${index}.service.id`,
                                            serviceId, // Set the service id correctly
                                          );
                                        }
                                      }}
                                    >
                                      <option>Sélectionner un service</option>
                                      {(() => {
                                        const budgetLine =
                                          formikRef.current.values.budgetLines[
                                          index
                                          ];
                                        let services = [];

                                        if (
                                          budgetLine &&
                                          budgetLine.service &&
                                          budgetLine.service.serviceCategoryId
                                        ) {
                                          services =
                                            servicesByCategory[
                                            budgetLine.service.serviceCategoryId
                                            ] || [];
                                          if (
                                            props.values.type === 'Financière'
                                          ) {
                                            services = services.filter(
                                              service =>
                                                service.collectionType !==
                                                'En Nature',
                                            );
                                          } else {
                                            services = services.filter(
                                              service =>
                                                service.collectionType !==
                                                'Financière',
                                            );
                                          }
                                        }

                                        return services.map(service => (
                                          <option
                                            key={service.id}
                                            value={service.id}
                                          >
                                            {service.name}
                                          </option>
                                        ));
                                      })()}
                                    </CustomSelect>
                                  </div>

                                </div>
                                {(() => {
                                  const budgetLine = formikRef.current.values.budgetLines[index];
                                  // Check if the service ID is selected
                                  if (budgetLine && budgetLine.service && (budgetLine.service.serviceCategoryId === 6 || budgetLine.service.serviceCategoryId === "6") && budgetLine.service.id) {
                                    return (
                                      <div className="d-flex justify-content-between align-items-center mb-2">
                                        <div className="form-group col-md-5 pl-0 mb-3">
                                          <CustomSelect
                                            disabled={statusConfig.disabled}
                                            label="Sélectionner un service de collect"
                                            name={`budgetLines.${index}.serviceCollectEpsId`}
                                            isRequired
                                            formProps={props}
                                          >
                                            <option value=''>Sélectionner un service de collect</option>
                                            {(() => {
                                              if (activeServiceCollectEps) {
                                                const serviceCollectEps = activeServiceCollectEps.filter(item => item.serviceId == budgetLine.service.id);
                                                if (serviceCollectEps) {
                                                  // If it's an array, map over it
                                                  if (Array.isArray(serviceCollectEps)) {
                                                    return serviceCollectEps.map(service => (
                                                      <option key={service.id} value={service.id}>
                                                        {service.name}
                                                      </option>
                                                    ));
                                                  }
                                                  // If it's just a single object, return that as the only option
                                                  else {
                                                    return (
                                                      <option key={serviceCollectEps.id} value={serviceCollectEps.id}>
                                                        {serviceCollectEps.name}
                                                      </option>
                                                    );
                                                  }
                                                }
                                              }
                                            })()}
                                          </CustomSelect>
                                        </div>
                                      </div>
                                    );
                                  }

                                  // If no service ID is selected, return null (don't render the component)
                                  return null;
                                })()}


                                <div className="d-flex justify-content-between align-items-center mb-2">
                                  <div className="form-group col-md-5 pl-0">
                                    <CustomTextInput
                                      label="Montant (DH)"
                                      disabled={statusConfig.disabled}
                                      isRequired
                                      name={`budgetLines.${index}.amount`}
                                      placeholder="500.00"
                                      formProps={props}
                                      type="number" // Changed to number type for better input handling
                                      style={{ width: '100%' }}
                                      onChange={e => {
                                        // Parse the input value correctly
                                        const rawValue = e.target.value;
                                        const newAmount = Number(rawValue) || 0;

                                        // Create updated budget lines array
                                        const updatedBudgetLines = props.values.budgetLines.map(
                                          (line, i) =>
                                            i === index
                                              ? { ...line, amount: newAmount }
                                              : line,
                                        );

                                        // Calculate total from UPDATED array
                                        const newTotal = updatedBudgetLines.reduce(
                                          (sum, line) =>
                                            sum + (Number(line.amount) || 0),
                                          0,
                                        );

                                        if (props.values.type === 'Nature') {
                                          // Update all values atomically
                                          props.setValues({
                                            ...props.values,
                                            budgetLines: updatedBudgetLines,
                                            value: newTotal,
                                            nonIdentifiedValue: 0,
                                          });
                                        } else {
                                          // Ensure budgetLines is updated for Financial type
                                          props.setValues({
                                            ...props.values,
                                            budgetLines: updatedBudgetLines,
                                          });

                                          // Calculate nonIdentifiedValue for Financial type
                                          const nonIdentifiedValue = Math.max(
                                            (props.values.value || 0) - newTotal,
                                            0,
                                          );
                                          props.setFieldValue(
                                            'nonIdentifiedValue',
                                            nonIdentifiedValue,
                                          );
                                        }
                                      }}
                                    />
                                  </div>
                                  <div className="form-group col-md-5 pl-0 mb-3">
                                    <CustomTextArea
                                      disabled={statusConfig.disabled}
                                      label="Description"
                                      placeholder="Commentaire"
                                      name={`budgetLines.${index}.comment`}
                                      style={{ width: '100%' }}
                                    />
                                  </div>
                                </div>
                                {props.values.type === 'Financière' && (
                                  <div className="d-flex justify-content-between align-items-center mb-2">
                                    {(formikRef.current.values.budgetLines[
                                      index
                                    ] &&
                                      formikRef.current.values.budgetLines[index]
                                        .service &&
                                      formikRef.current.values.budgetLines[index]
                                        .service.propositionSystem &&
                                      props.values.budgetLines[index].status !==
                                      'REMAINING') ||
                                      (props.values.budgetLines[index] &&
                                        props.values.budgetLines[index].service &&
                                        props.values.budgetLines[index].service
                                          .propositionSystem &&
                                        props.values.budgetLines[index].status !==
                                        'REMAINING') ? (
                                      <div className="form-group col-md-5 pl-0 mb-3">
                                        <CustomTextInput
                                          disabled={
                                            statusConfig.disabled &&
                                            !props.values.budgetLines[index]
                                              .makeItAvailable
                                          }
                                          label="Montant Par Bénéficiaire (DH)"
                                          name={`budgetLines.${index}.amountByBeneficiary`}
                                          placeholder="500.00"
                                          formProps={props}
                                          type="text"
                                          style={{ width: '100%' }}
                                        />
                                      </div>
                                    ) : null}
                                  </div>
                                )}
                                {props.values.type === 'Nature' && (
                                  <div className="d-flex justify-content-between align-items-center mb-2">
                                    <div className="form-group col-md-5 pl-0 mb-3">
                                      <TypeProductNatures
                                        value=""
                                        disabled={statusConfig.disabled}
                                        name={`budgetLines.${index}.productCategory`}
                                        formProps={props}
                                        label="Types de produits"
                                        isRequired
                                        onChange={selectedOptions => {
                                          setFieldValue(
                                            `budgetLines[${index}].productCategory`,
                                            selectedOptions.map(option => ({
                                              id: option.value,
                                            })), // Ensure objects
                                          );
                                        }}
                                      />
                                    </div>
                                  </div>
                                )}
                                {props.values.budgetLines.length > 1 ||
                                  props.values.type === 'Financière' ? (
                                  <button
                                    type="button"
                                    disabled={statusConfig.disabled}
                                    onClick={() => {
                                      // Get the current amount of the budget line being removed
                                      const amountToRemove =
                                        parseFloat(
                                          props.values.budgetLines[index].amount,
                                        ) || 0;

                                      // Remove the budget line
                                      remove(index);

                                      // Calculate the total of the remaining budget lines after removal
                                      const totalBudgetLines = props.values.budgetLines.reduce(
                                        (sum, line) =>
                                          sum + (parseFloat(line.amount) || 0),
                                        0,
                                      );

                                      // Calculate the total value from the form
                                      const totalValue = props.values.value || 0; // Assuming this is the correct reference

                                      // Update the nonIdentifiedValue: Add the amount of the removed line back
                                      if (props.values.type === 'Nature') {
                                        // Update the non-identified value in the form state
                                        props.setFieldValue(
                                          'value',
                                          totalValue - amountToRemove,
                                        );
                                      } else {
                                        const newNonIdentifiedValue = Math.max(
                                          totalValue -
                                          totalBudgetLines +
                                          amountToRemove,
                                          0,
                                        );

                                        // Update the non-identified value in the form state
                                        props.setFieldValue(
                                          'nonIdentifiedValue',
                                          newNonIdentifiedValue,
                                        );
                                      }
                                    }}
                                    className="btn btn-outline-danger"
                                    style={{ fontSize: '1rem',borderRadius: '35px' }}
                                  >
                                    <span className="fa fa-trash"></span>{' '}
                                    Supprimer
                                  </button>
                                ) : null}
                              </div>
                            );
                          })}
                        </div>
                      )}
                  </div>
                )}
              </FieldArray>
              {props.values.type === 'Financière' && (
                <>
                  <div className="d-flex justify-content-between align-items-center mb-4">
                    {' '}
                  </div>
                  <div className="d-flex flex-column">
                    <div
                      className="d-flex justify-content-between align-items-center"
                      style={{ width: '100%', borderBottom: '1px solid red' }}
                    >
                      <div
                        style={{
                          fontWeight: 'bold',
                          fontSize: '1.25rem',
                          marginBottom: '0.5rem',
                        }}
                      >
                        Ligne de donation non identifiée
                      </div>
                    </div>

                    <div
                      style={{
                        // Darker grey border for non-identified
                        borderRadius: '0.25rem',
                        padding: '1rem',
                        marginBottom: '1rem',
                        marginTop: '1rem',
                        // agrey background for a calm feel
                        backgroundColor: '#f5f5f5 ',
                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', // Subtle shadow for depth
                      }}
                    >
                      {(props.values.nonIdentifiedStatus === 'EXECUTED' ||
                        props.values.nonIdentifiedStatus === 'REMAINING' ||
                        props.values.nonIdentifiedStatus === 'RESERVED') && (
                          <span
                            style={{
                              border: '1px solid #dc3545', // Darker grey border for non-identified
                              borderRadius: '0.25rem',
                              padding: '1rem',
                              marginBottom: '1rem',
                              marginTop: '1rem',
                              backgroundColor: '#fefefe', // Slightly off-white background
                              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', // Subtle shadow for depth
                            }}
                          >
                            Exécutée (Non modifiable)
                          </span>
                        )}
                      <div className="d-flex justify-content-between align-items-center mb-2">
                        <div className="form-group col-md-5 pl-0">
                          <CustomTextInput
                            label="Montant (DH)"
                            name="nonIdentifiedValue"
                            placeholder="500.00"
                            formProps={props}
                            type="text"
                            style={{ width: '100%' }}
                            disabled={
                              props.values.nonIdentifiedStatus === 'EXECUTED' ||
                              props.values.nonIdentifiedStatus === 'RESERVED'
                            }
                          />
                        </div>
                        <div className="form-group col-md-5 pl-0 mb-3">
                          <CustomTextArea
                            label="Description"
                            name="nonIdentifiedComment"
                            placeholder="Commentaire"
                            disabled={
                              props.values.nonIdentifiedStatus === 'EXECUTED' ||
                              props.values.nonIdentifiedStatus === 'RESERVED'
                            }
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>

            <div className="d-flex justify-content-end">


              <button
                className="btn-style primary mr-2"
                type="button"
                onClick={props.submitForm}
                disabled={props.isSubmitting || isLoading}
              >
                {isSubmitting
                  ? 'En cours...'
                  : globalProps.edit || params.idDonation
                    ? 'Modifier donation'
                    : 'Enregistrer donation'}
              </button>

              <button
                className="btn-style secondary "
                type="button"
                onClick={() => {
                  if (location.state && location.state.redirectTo) {
                    history.push({
                      pathname: `/donations/fiche/${params.idDonation}/info`,
                    });
                  } else {
                    history.push({
                      pathname: '/donations',
                    });
                  }
                }}
              >
                Annuler
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}

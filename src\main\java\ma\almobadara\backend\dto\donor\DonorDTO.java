package ma.almobadara.backend.dto.donor;

import lombok.*;
import lombok.experimental.SuperBuilder;
import ma.almobadara.backend.dto.CityWithRegionAndCountryDTO;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.communs.DocumentDTO;
import ma.almobadara.backend.dto.communs.NoteDTO;
import ma.almobadara.backend.dto.donation.DonationDTO;
import ma.almobadara.backend.dto.referentiel.CityDTO;
import ma.almobadara.backend.dto.referentiel.DonorStatusDTO;
import ma.almobadara.backend.dto.takenInCharge.TakenInChargeDonorDTO;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@ToString
public class DonorDTO {

	private Long id;
	private Double balance;
	private String identityCode;
	private String address;
	private String addressAr;
	private String codeComptabilite;
	private String comment;
	private String label;
	private Long anonymeId;
	private List<NoteDTO> notes;
	private List<DocumentDTO> documentDonors;
	private DonorStatusDTO status;
	private LocalDateTime createdAt;
	private CityDTO city;
	private CityWithRegionAndCountryDTO info;
	private List<DonationDTO> donations;
	private List<TakenInChargeDonorDTO> takenInChargeDonors;
	private String code;
	private String firstDonationYear;
	private Double availableBalance;
	private Double totalDonated;
	private Double totalExecuted;
	private Double totalReserved;
	private Double availableBalanceInKafalat;
	private Double availableBalanceInAideComplementaire;
	private Double empruntSold; // Total emprunts minus total remboursements
	private List<TagDTO> tags;



}

import React, { useState, useEffect } from 'react';
import CommunCaisseForm from './CommunCaisseForm';
import { useHistory, useParams } from 'react-router-dom';
import { Alert } from 'react-bootstrap';
import { useSelector, useDispatch } from 'react-redux';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import { createStructuredSelector } from 'reselect';
import { makeSelectLoading, makeSelectError, makeSelectSuccess, makeSelectCurrentCaisse } from '../selectors';
import communCaisseSaga from '../saga';
import communCaisseReducer from '../reducer';
import { resetError, loadCaisseById } from '../actions';
import { COMMUN_CAISSE_LIST_KEY } from '../constants';

const key = COMMUN_CAISSE_LIST_KEY;

const stateSelector = createStructuredSelector({
    loading: makeSelectLoading,
    error: makeSelectError,
    success: makeSelectSuccess,
    currentCaisse: makeSelectCurrentCaisse,
});

export default function AddOrUpdateCaisse() {
    useInjectReducer({ key, reducer: communCaisseReducer });
    useInjectSaga({ key, saga: communCaisseSaga });

    const history = useHistory();
    const { id } = useParams();
    const dispatch = useDispatch();
    const { loading, error, success, currentCaisse } = useSelector(stateSelector);

    const [showAlert, setShowAlert] = useState(false);
    const [alertMessage, setAlertMessage] = useState('');

    // Determine if this is update mode based on URL parameter
    const isUpdate = !!id;

    // Load caisse data for edit mode
    useEffect(() => {
        if (isUpdate && id) {
            dispatch(loadCaisseById(id));
        }
    }, [isUpdate, id, dispatch]);

    // If currentCaisse is not provided, use defaults for add
    const defaultValues = {
        donor: '',
        amount: '',
        dateEmprunt: '',
        description: '',
        service: {
            serviceCategoryId: '',
            id: '',
            name: '',
        },
    };

    // Transform currentCaisse data for form if in edit mode
    const getFormInitialValues = () => {
        if (isUpdate && currentCaisse) {
            return {
                donor: { id: currentCaisse.donorId, name: currentCaisse.donorName },
                amount: currentCaisse.globalAmount || 0,
                dateEmprunt: currentCaisse.lastDateEmprunt ? currentCaisse.lastDateEmprunt.split('T')[0] : '', // Extract date part
                description: currentCaisse.description || '',
                service: {
                    serviceCategoryId: currentCaisse.serviceCategoryId || '',
                    id: currentCaisse.serviceId || '',
                    name: currentCaisse.serviceName || '',
                },
            };
        }
        return defaultValues;
    };

    const formInitialValues = getFormInitialValues();

    // Handle success - redirect to list and show success message
    useEffect(() => {
        if (success) {
            // Set a flag in sessionStorage to indicate a successful add operation
            if (!isUpdate) {
                sessionStorage.setItem('caisseAddSuccess', 'true');
            }
            setShowAlert(true);
            setAlertMessage(isUpdate ? 'Caisse modifiée avec succès !' : 'La caisse emprunt est ajoutée avec succès !');
            setShowAlert(false);
            dispatch(resetError()); // Reset state
            history.push('/commun-caisse'); // Redirect to list page after success
        }
    }, [success, isUpdate, history, dispatch]);

    // Handle error - don't redirect, just show error (error display is handled in the form)
    useEffect(() => {
        if (error) {
            // Error is displayed in the form component, no redirect
            setTimeout(() => {
                dispatch(resetError()); // Reset error after 5 seconds
            }, 5000);
        }
    }, [error, dispatch]);

    const handleSubmit = () => {
        // Form submission is handled by the form component via Redux
        // This function is called after form validation passes
    };

    return (
        <div style={{ maxWidth: 800, margin: '0 auto', padding: '2rem 1rem' }}>
            <h2 style={{ marginBottom: '2rem' }}>{isUpdate ? 'Modifier une caisse' : 'Ajouter un emprunt'}</h2>
            {showAlert && (
                <Alert variant="success" onClose={() => setShowAlert(false)} dismissible>{alertMessage}</Alert>
            )}
            <CommunCaisseForm
                initialValues={formInitialValues}
                onSubmit={handleSubmit}
                isUpdate={isUpdate}
            />
        </div>
    );
} 
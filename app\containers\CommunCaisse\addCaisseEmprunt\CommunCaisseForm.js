import React from 'react';
import { Form, Formik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Grid } from '@mui/material';
import { Alert } from 'react-bootstrap';
import { addCaisseRequest, updateCaisseRequest } from '../actions';
import { CustomTextInput } from 'containers/Common/CustomInputs/CustomTextInput';
import * as Yup from 'yup';
import { createStructuredSelector } from 'reselect';
import ChooseDonor from 'containers/Common/SubComponents/ChooseDonor';
import { makeSelectLoading, makeSelectError } from '../selectors';
import ServiceCategories from 'containers/Common/SubComponents/ServiceCategories';
import Services from 'containers/Common/SubComponents/Services';



const validationSchema = Yup.object({
    donor: Yup.object().required('Le donateur est obligatoire'),
    amount: Yup.number().required('Le montant est obligatoire'),
    dateEmprunt: Yup.date().required('La date d\'emprunt est obligatoire'),
    description: Yup.string(),
    service: Yup.object().shape({
        serviceCategoryId: Yup.number().required('La catégorie de service est obligatoire'),
        id: Yup.number().required('Le service est obligatoire'),
    }),
});

const stateSelector = createStructuredSelector({
    loading: makeSelectLoading,
    error: makeSelectError,
});

export default function CommunCaisseForm({ initialValues, onSubmit, isUpdate = false }) {
    const dispatch = useDispatch();
    const { loading, error } = useSelector(stateSelector);

    // Category selection handler
    const SelectCategoryHandler = () => {
        // Category selection is handled by the ServiceCategories component
    };

    return (
        <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            enableReinitialize
            onSubmit={values => {
                if (isUpdate) {
                    dispatch(updateCaisseRequest(values));
                } else {
                    dispatch(addCaisseRequest(values));
                }
                if (onSubmit) onSubmit();
            }}
        >
            {formikProps => (
                <div style={{ background: '#fff', padding: '2rem', borderRadius: '12px' }}>
                    {error && (
                        <Alert variant="danger" style={{ marginBottom: '1rem' }}>
                            {(error.response && error.response.data && error.response.data.detailedMessage) || error.message || 'Une erreur est survenue'}
                        </Alert>
                    )}
                    <Form onSubmit={formikProps.handleSubmit}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12}>
                                <div style={{ width: '50%', margin: '0 auto' }}>
                                    <ChooseDonor props={formikProps} />
                                </div>
                            </Grid>

                            <Grid item xs={12} sm={12}>
                                <CustomTextInput name="amount" label="Montant *" type="number" formProps={formikProps} />
                            </Grid>

                            {/* Service Selection */}
                            <Grid item xs={12} sm={12}>
                                <ServiceCategories
                                    name="serviceCategoryId"
                                    value={formikProps.values.serviceCategoryId}
                                    label="Catégorie"
                                    // formProps={props} 
                                    onSelectCanal={SelectCategoryHandler}
                                    isRequired
                                />
                            </Grid>
                             {/* Service Selection */}
                            <Grid item xs={12} sm={12}>
                                <ServiceCategories
                                    name="serviceCategoryId"
                                    value={formikProps.values.serviceCategoryId}
                                    label="Catégorie"
                                    // formProps={props} 
                                    onSelectCanal={SelectCategoryHandler}
                                    isRequired
                                />
                            </Grid>
                            <Grid item xs={12} sm={12}>
                                <CustomTextInput
                                    name="dateEmprunt"
                                    label="Date Emprunt *"
                                    type="date"
                                    formProps={formikProps}
                                    max={new Date().toISOString().split('T')[0]}
                                />
                            </Grid>
                            <Grid item xs={12} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                                <Button type="submit" variant="contained" color="primary" disabled={loading}>
                                    {loading ? 'En cours...' : (isUpdate ? "Modifier la caisse" : "Ajouter la caisse")}
                                </Button>
                            </Grid>
                        </Grid>
                    </Form>
                </div>
            )}
        </Formik>
    );
}

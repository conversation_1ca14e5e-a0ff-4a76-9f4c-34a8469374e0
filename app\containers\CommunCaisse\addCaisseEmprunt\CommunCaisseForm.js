import React, { useState, useEffect } from 'react';
import { Form, Formik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Grid } from '@mui/material';
import { Alert } from 'react-bootstrap';
import { addCaisseRequest, updateCaisseRequest } from '../actions';
import { CustomTextInput } from 'containers/Common/CustomInputs/CustomTextInput';
import { CustomSelect } from 'containers/Common/CustomInputs/CustomSelect';
import * as Yup from 'yup';
import { createStructuredSelector } from 'reselect';
import ChooseDonor from 'containers/Common/SubComponents/ChooseDonor';
import { makeSelectLoading, makeSelectError } from '../selectors';
import ServiceCategories from 'containers/Common/SubComponents/ServiceCategories';
import { fetchActiveServicesRequest } from 'containers/Service/ListService/actions';
import { makeSelectActiveServices } from 'containers/Service/ListService/selectors';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import serviceReducer from 'containers/Service/ListService/reducer';
import serviceSaga from 'containers/Service/ListService/saga';



const validationSchema = Yup.object({
    donor: Yup.object().required('Le donateur est obligatoire'),
    amount: Yup.number().required('Le montant est obligatoire'),
    dateEmprunt: Yup.date().required('La date d\'emprunt est obligatoire'),
    description: Yup.string(),
    serviceCategoryId: Yup.number().required('La catégorie de service est obligatoire'),
    serviceId: Yup.number().required('Le service est obligatoire'),
});

const stateSelector = createStructuredSelector({
    loading: makeSelectLoading,
    error: makeSelectError,
});

const serviceSelector = createStructuredSelector({
    activeServices: makeSelectActiveServices,
});

export default function CommunCaisseForm({ initialValues, onSubmit, isUpdate = false }) {
    const dispatch = useDispatch();
    const { loading, error } = useSelector(stateSelector);
    const { activeServices } = useSelector(serviceSelector);

    // Inject reducers and sagas for service management
    useInjectReducer({ key: 'serviceList', reducer: serviceReducer });
    useInjectSaga({ key: 'serviceList', saga: serviceSaga });

    // State for managing services by category
    const [servicesByCategory, setServicesByCategory] = useState();

    // Load services when category changes
    useEffect(() => {
        if (servicesByCategory) {
            dispatch(fetchActiveServicesRequest(servicesByCategory));
        }
    }, [servicesByCategory, dispatch]);

    // Category selection handler
    const SelectCategoryHandler = value => {
        setServicesByCategory(value.id);
    };

    // Create activeServicesList for the dropdown
    let activeServicesList = null;
    if (activeServices) {
        activeServicesList = activeServices.map(activeService => (
            <option key={activeService.id} value={activeService.id}>
                {activeService.name}
            </option>
        ));
    }

    return (
        <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            enableReinitialize
            onSubmit={values => {
                if (isUpdate) {
                    dispatch(updateCaisseRequest(values));
                } else {
                    dispatch(addCaisseRequest(values));
                }
                if (onSubmit) onSubmit();
            }}
        >
            {formikProps => (
                <div style={{ background: '#fff', padding: '2rem', borderRadius: '12px' }}>
                    {error && (
                        <Alert variant="danger" style={{ marginBottom: '1rem' }}>
                            {(error.response && error.response.data && error.response.data.detailedMessage) || error.message || 'Une erreur est survenue'}
                        </Alert>
                    )}
                    <Form onSubmit={formikProps.handleSubmit}>
                        <Grid container spacing={2}>
                            <Grid item xs={12} sm={12}>
                                <div style={{ width: '50%', margin: '0 auto' }}>
                                    <ChooseDonor props={formikProps} />
                                </div>
                            </Grid>

                            <Grid item xs={12} sm={12}>
                                <CustomTextInput name="amount" label="Montant *" type="number" formProps={formikProps} />
                            </Grid>

                            {/* Service Category Selection */}
                            <Grid item xs={12} sm={6}>
                                <ServiceCategories
                                    name="serviceCategoryId"
                                    value={formikProps.values.serviceCategoryId}
                                    label="Catégorie"
                                    onSelectCanal={SelectCategoryHandler}
                                    isRequired
                                    disabled={isUpdate}
                                />
                            </Grid>

                            {/* Service Selection */}
                            <Grid item xs={12} sm={6}>
                                <CustomSelect
                                    unique
                                    label="Service"
                                    isRequired
                                    name="serviceId"
                                    formProps={formikProps}
                                    onChange={event => {
                                        const selectedId = event.target.value;
                                        formikProps.setFieldValue('serviceId', selectedId);
                                        // Handle service change if needed
                                        const selectedService = activeServices && activeServices.find(
                                            service => service.id === Number(selectedId)
                                        );
                                        if (selectedService) {
                                            // You can add additional logic here if needed
                                        }
                                    }}
                                    disabled={isUpdate}
                                >
                                    <option value="">-- Service --</option>
                                    {activeServicesList}
                                </CustomSelect>
                            </Grid>
                            <Grid item xs={12} sm={12}>
                                <CustomTextInput
                                    name="dateEmprunt"
                                    label="Date Emprunt *"
                                    type="date"
                                    formProps={formikProps}
                                    max={new Date().toISOString().split('T')[0]}
                                />
                            </Grid>
                            <Grid item xs={12} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                                <Button type="submit" variant="contained" color="primary" disabled={loading}>
                                    {loading ? 'En cours...' : (isUpdate ? "Modifier la caisse" : "Ajouter la caisse")}
                                </Button>
                            </Grid>
                        </Grid>
                    </Form>
                </div>
            )}
        </Formik>
    );
}

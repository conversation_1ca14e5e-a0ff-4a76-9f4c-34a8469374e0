import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Field, useField } from 'formik';
import { createStructuredSelector } from 'reselect';
import { useInjectReducer, useInjectSaga } from 'redux-injectors';
import style from 'Css/sideBar.css';
import stylesList from 'Css/profileList.css';
import { Link } from 'react-router-dom';
import { COMAPNY_PICTURE } from 'components/Common/ListIcons/ListIcons';
import ReactHtmlParser from 'react-html-parser';
import { Modal, Button, Form, Card } from 'react-bootstrap';
import CustomCheckBox from 'components/Common/CustomCheckBox';
import { loadDonors } from '../../../Donor/Donors/actions';
import { makeSelectDonors } from '../../../Donor/Donors/selectors';
import reducer from '../../../Donor/Donors/reducer';
import sagaDonor from '../../../Donor/Donors/saga';
import infoIcon from '../../../../images/icons/info.svg';
import image from '../../../../images/user.png';
import RefreshIcon from '@mui/icons-material/Refresh';


const omdbSelector = createStructuredSelector({
  donors: makeSelectDonors,
});

const key = 'donors';

const ChooseMultiDonorModal = props => {
  const { onConfirmSelection, getedSelectedDonors, show, onHide } = props;
  const { donors } = useSelector(omdbSelector);
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga: sagaDonor });
  const [errorMessages, setErrorMessages] = useState('');

  props = props.formProps;
  const [donorResult, setDonorResult] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // Set default donor type to "Physique"
  const [selectedDonorType, setSelectedDonorType] = useState('Physique');
  const dispatch = useDispatch();
  const [selectedDonors, setSelectedDonors] = useState([]);

  useEffect(() => {
    if (getedSelectedDonors) {
      setSelectedDonors(getedSelectedDonors);
    }
  }, [getedSelectedDonors]);

  const selectDonorHandler = donor => {
    setSelectedDonors(prevSelected => {
      // Si le donateur est déjà sélectionné, le désélectionner
      if (prevSelected.some(d => d.id === donor.id)) {
        return [];
      }
      // Sinon, remplacez la sélection par ce donateur uniquement
      onConfirmSelection([
        {
          id: donor.id,
          type: donor.type,
          name: `${
            donor.firstName ? `${donor.firstName} ` : ''
          }${donor.lastName || donor.company || donor.name}`,
          picture64: donor.picture64,
          empruntSold: donor.empruntSold || 0,
        },
      ]);
      onHide();
      return [
        {
          id: donor.id,
          type: donor.type,
          name: `${
            donor.firstName ? `${donor.firstName} ` : ''
          }${donor.lastName || donor.company || donor.name}`,
          picture64: donor.picture64,
          empruntSold: donor.empruntSold || 0,
        },
      ];
    });
  };

  const handleConfirm = () => {
    onConfirmSelection(selectedDonors);
    onHide();
  };

  useEffect(() => {
    if (donors && selectedDonorType) {
      setDonorResult(donors);
      setLoading(false);
    }
  }, [donors, selectedDonorType]);

  useEffect(() => {
    if (selectedDonorType) {
      dispatch(loadDonors(currentPage - 1, selectedDonorType));
    }
  }, [selectedDonorType]);

  const nextPage = () => {
    if (donorResult && !donorResult.last) {
      setLoading(true);
      setCurrentPage(prevPage => prevPage + 1);
      dispatch(loadDonors(currentPage, selectedDonorType));
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setLoading(true);
      setCurrentPage(prevPage => prevPage - 1);
      dispatch(loadDonors(currentPage - 2, selectedDonorType));
    }
  };

  const handleSearchByType = event => {
    setCurrentPage(1);
    setSelectedDonorType(event.target.value);
    setLoading(true);
    dispatch(loadDonors(currentPage - 1, event.target.value));
  };

  const handleSearchByFullNameDonorPhysical = () => {
    setLoading(true);
    const searchTerm = props.values.fullName ? props.values.fullName.toLowerCase() : null;
    const [firstName, lastName] = searchTerm ? searchTerm.split(' ') : [null, null];
    dispatch(
      loadDonors(currentPage - 1, selectedDonorType, firstName, lastName),
    );
  };

  const handleSearchByAnonymousDonor = () => {
    setLoading(true);
    const anonymeDonor = props.values.anonymousSearch ? props.values.anonymousSearch.toLowerCase() : null;
    dispatch(
      loadDonors(
        currentPage - 1,
        selectedDonorType,
        null,
        null,
        null,
        null,
        anonymeDonor,
      ),
    );
  };

  const handleSearchByNameDonorMoral = () => {
    setLoading(true);
    const company = props.values.company ? props.values.company.toLowerCase() : null;
    dispatch(
      loadDonors(currentPage - 1, selectedDonorType, null, null, company),
    );
  };

  const handleReset = () => {
    setLoading(true);
    // Reset the form values
    if (props.setFieldValue) {
      if (selectedDonorType === 'Physique') {
        props.setFieldValue('fullName', '');
      } else if (selectedDonorType === 'Moral') {
        props.setFieldValue('company', '');
      } else if (selectedDonorType === 'Anonyme') {
        props.setFieldValue('anonymousSearch', '');
      }
    }
    // Load all donors of current type
    dispatch(loadDonors(currentPage - 1, selectedDonorType));
  };

  const highlightSearchTerm = (name, searchTerm) => {
    const regex = new RegExp(searchTerm, 'gi');
    return name.replace(regex, match => `<b>${match}</b>`);
  };

  let data = null;
  if (donorResult && donorResult.content) {
    const sortedDonors = [...donorResult.content].sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'Physique' ? -1 : 1;
      }

      if (a.type === 'Physique') {
        const firstNameA = (a.firstName || '').toLowerCase();
        const firstNameB = (b.firstName || '').toLowerCase();
        if (firstNameA !== firstNameB) {
          return firstNameA.localeCompare(firstNameB);
        }

        const lastNameA = (a.lastName || '').toLowerCase();
        const lastNameB = (b.lastName || '').toLowerCase();
        return lastNameA.localeCompare(lastNameB);
      }
      if (a.type === 'Anonyme') {
        const anonymousA = (a.anonymous || '').toLowerCase();
        const anonymousB = (b.anonymous || '').toLowerCase();
        return anonymousA.localeCompare(anonymousB);
      }

      const companyA = (a.company || '').toLowerCase();
      const companyB = (b.company || '').toLowerCase();
      return companyA.localeCompare(companyB);
    });

    data = (
      <ul
        className="list-group mt-4"
        style={{ maxHeight: '250px', overflowY: 'auto', padding: '0' }}
      >
        {sortedDonors &&
          sortedDonors.map(donor => (
            <li
              key={donor.id}
              className="list-group-item d-flex align-items-center"
              title="Cliquez pour sélectionner"
              style={{
                height: '60px',
                cursor: 'pointer',
                padding: '0.5rem 1rem',
                borderRadius: '8px',
                backgroundColor: selectedDonors.some(d => d.id === donor.id)
                  ? '#f0f8ff'
                  : '#ffffff',
                border: selectedDonors.some(d => d.id === donor.id)
                  ? '2px solid #4F89D7'
                  : '1px solid #ddd',
                marginBottom: '8px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}
              onClick={() => selectDonorHandler(donor)}
              onMouseEnter={e =>
                (e.currentTarget.style.backgroundColor = '#f0f8ff')
              }
              onMouseLeave={e =>
                (e.currentTarget.style.backgroundColor = selectedDonors.some(
                  d => d.id === donor.id,
                )
                  ? '#f0f8ff'
                  : '#ffffff')
              }
            >
              {/* Image or Logo Column */}
              <div
                style={{
                  padding: '5px',
                  textAlign: 'center',
                  marginRight: '10px',
                }}
              >
                <img
                  style={{
                    width: '40px',
                    height: '40px',
                    borderRadius: '50%',
                  }}
                  src={
                    donor.type === 'Physique'
                      ? donor.pictureUrl
                        ? `data:image/png;base64,${atob(donor.picture64)}`
                        : image // Fallback for Physique donor
                      : donor.type === 'Moral'
                        ? donor.logoUrl
                          ? `data:image/png;base64,${atob(donor.logo64)}`
                          : COMAPNY_PICTURE // Fallback for Moral donor
                        : image // For Anonyme or others
                  }
                  className={`rounded-circle ${style.imgBorder2}`}
                  alt={donor.type}
                />
              </div>

              {/* Donor Name/Company Column */}
              <div className="p-1" style={{ flex: 1, fontWeight: '600' }}>
                {donor.type === 'Physique' ? (
                  <span>
                    {props.values.fullName
                      ? ReactHtmlParser(
                        highlightSearchTerm(
                          `${donor.firstName} ${donor.lastName}`,
                          props.values.fullName,
                        ),
                      )
                      : `${donor.firstName} ${donor.lastName}`}
                  </span>
                ) : donor.type === 'Anonyme' ? (
                  <span>
                    {props.values.name
                      ? ReactHtmlParser(
                        highlightSearchTerm(donor.name, props.values.name),
                      )
                      : donor.name}
                  </span>
                ) : (
                  <span>
                    {props.values.company
                      ? ReactHtmlParser(
                        highlightSearchTerm(
                          `${donor.company}`,
                          props.values.company,
                        ),
                        )
                      : `${donor.company}`}
                  </span>
                )}
              </div>
            </li>
          ))}
      </ul>
    );
  }

  const noDataMessage =
    selectedDonorType &&
    donorResult &&
    donorResult.content &&
    donorResult.content.length === 0 ? (
      <p style={{ textAlign: 'center', marginTop: '20px' }}>
        Aucun donateur trouvé
      </p>
    ) : null;

  const paginationControls = selectedDonorType && (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: '10px',
      }}
    >
      <Button
        variant="outline-primary"
        disabled={currentPage === 1}
        onClick={prevPage}
      >
        {'<'}
      </Button>
      <span className="mx-3 fw-bold">
        Page {currentPage} sur {donorResult && donorResult.totalPages}
      </span>
      <Button
        variant="outline-primary"
        type="button"
        disabled={donorResult && donorResult.last}
        onClick={nextPage}
      >
        {'>'}
      </Button>
    </div>
  );

  const handleClose = () => {
    onHide();
    setErrorMessages('');
  };

  return (
    <Modal show={show} onHide={handleClose} centered size="xl">
      <Modal.Header closeButton style={{ borderBottom: '2px solid #e9ecef' }}>
        <Modal.Title>Choisir un donateur</Modal.Title>
      </Modal.Header>

      <Modal.Body
        style={{ maxHeight: 'calc(100vh - 200px)', overflowY: 'auto' }}
      >
        {errorMessages && (
          <div
            className="alert alert-danger mb-3"
            role="alert"
            style={{ fontWeight: 'bold', fontSize: '14px' }}
          >
            {errorMessages}
          </div>
        )}

        <Form>
          {/* Donor Type Radio Buttons */}
          <div className="d-flex justify-content-center align-items-center gap-20 mb-3">
            <div>
              <Form.Label className="text-muted" style={{ fontWeight: 'bold' }}>
                Type des donateurs
              </Form.Label>
            </div>
            {['Physique', 'Moral', 'Anonyme'].map(type => (
              <CustomCheckBox
                key={type}
                className="mw-110"
                id={`donor${type[0]}`}
                value={type}
                label={type}
                onClick={handleSearchByType}
                checked={selectedDonorType === type}
                isChecked={selectedDonorType === type}
              />
            ))}
          </div>

          {/* Conditional Input Fields Based on Donor Type */}
          {selectedDonorType === 'Physique' ? (
            <div className="form-group row m-2 align-items-center justify-content-center">
              <div className="col-2 text-center">
                <label>
                  <b>Physique</b>
                </label>
              </div>
              <div className="col-4">
                <Field
                  type="text"
                  placeholder="Prénom ou Nom"
                  className="form-control"
                  name="fullName"
                  style={{
                    borderRadius: '0.375rem',
                    padding: '0.75rem',
                    fontSize: '16px',
                    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
                    transition: 'box-shadow 0.3s ease',
                  }}
                />
              </div>
              <div className="col-4 d-flex gap-2">
                <Button
                  variant="primary"
                  onClick={handleSearchByFullNameDonorPhysical}
                  style={{
                    padding: '0.75rem 1.5rem',
                    fontSize: '16px',
                    borderRadius: '0.375rem',
                  }}
                >
                  Filtrer
                </Button>
                <Button
                  variant="outline-secondary"
                  onClick={handleReset}
                  style={{
                    padding: '0.75rem',
                    fontSize: '16px',
                    borderRadius: '0.375rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  title="Réinitialiser"
                >
                  <RefreshIcon/>
                </Button>
              </div>
            </div>
          ) : selectedDonorType === 'Moral' ? (
            <div className="form-group row m-2 align-items-center justify-content-center">
              <div className="col-2 text-center">
                <label>
                  <b>Moral</b>
                </label>
              </div>
              <div className="col-4">
                <Field
                  type="text"
                  placeholder="Nom de l'entreprise"
                  name="company"
                  className="form-control"
                  style={{
                    borderRadius: '0.375rem',
                    padding: '0.75rem',
                    fontSize: '16px',
                    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
                    transition: 'box-shadow 0.3s ease',
                  }}
                />
              </div>
              <div className="col-4 d-flex gap-2">
                <Button
                  variant="primary"
                  onClick={handleSearchByNameDonorMoral}
                  style={{
                    padding: '0.75rem 1.5rem',
                    fontSize: '16px',
                    borderRadius: '0.375rem',
                  }}
                >
                  Filtrer
                </Button>
                <Button
                  variant="outline-secondary"
                  onClick={handleReset}
                  style={{
                    padding: '0.75rem',
                    fontSize: '16px',
                    borderRadius: '0.375rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  title="Réinitialiser"
                >
                  <RefreshIcon/>
                </Button>
              </div>
            </div>
          ) : selectedDonorType === 'Anonyme' ? (
            <div className="form-group row m-2 align-items-center justify-content-center">
              <div className="col-2 text-center">
                <label>
                  <b>Anonyme</b>
                </label>
              </div>
              <div className="col-4">
                <Field
                  type="text"
                  placeholder="Rechercher un donateur anonyme"
                  className="form-control"
                  name="anonymousSearch"
                  style={{
                    borderRadius: '0.375rem',
                    padding: '0.75rem',
                    fontSize: '16px',
                    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
                    transition: 'box-shadow 0.3s ease',
                  }}
                />
              </div>
              <div className="col-4 d-flex gap-2">
                <Button
                  variant="primary"
                  onClick={handleSearchByAnonymousDonor}
                  style={{
                    padding: '0.75rem 1.5rem',
                    fontSize: '16px',
                    borderRadius: '0.375rem',
                  }}
                >
                  Filtrer
                </Button>
                <Button
                  variant="outline-secondary"
                  onClick={handleReset}
                  style={{
                    padding: '0.75rem',
                    fontSize: '16px',
                    borderRadius: '0.375rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                  title="Réinitialiser"
                >
                  <RefreshIcon/>
                </Button>
              </div>
            </div>
          ) : (
            !selectedDonorType && (
              <div
                  className="m-1"
                  style={{
                    minHeight: 'auto',
                    display: 'flex',
                    alignItems: 'center',
                }}
                >
                  <div
                    style={{
                      whiteSpace: 'nowrap',
                      fontStyle: 'italic',
                    color: '#777',
                    }}
                  >
                  Aucun type de donateur sélectionné
                  </div>
                </div>
              )
          )}
        </Form>

        {/* Loading State */}
        {loading ? (
          <div className="d-flex justify-content-center pb-3">
            <div
              className="spinner-border"
              style={{ width: '4rem', height: '4rem' }}
              role="status"
            >
              <span className="sr-only">Loading...</span>
            </div>
          </div>
        ) : (
          data
        )}

        {noDataMessage}

        {paginationControls}
      </Modal.Body>
    </Modal>
  );
};

export default function ChooseMultiDonors(props) {
  const { onDonorsSelect, type, getedSelectedDonors, disabled } = props;
  const formProps = props.props;
  const [field, meta] = useField({ name: 'takenInChargeDonors' });
  const [selectedDonors, setSelectedDonors] = useState([]);
  const [showModal, setShowModal] = useState(false);

  const handleConfirmSelection = donors => {
    setSelectedDonors(donors);
    if (onDonorsSelect) {
      onDonorsSelect(donors);
    }
  };
  return (
    <div className={stylesList.fix2}>
      <div className={stylesList.fix}>
        {!disabled && (
          <div className="d-flex align-items-center mb-3">
            <h5
              className="mr-2"
              style={{ fontWeight: '600', color: '#333', marginBottom: 0 }}
            >
              Donateur :
            </h5>

            <a
              disabled={disabled}
              href="#"
              onClick={e => {
                e.preventDefault();
                setShowModal(true);
              }}
              style={{
                fontSize: '18px',
                textDecoration: 'underline',
                color: '#4F89D7',
                cursor: 'pointer',
                transition: 'color 0.2s',
                marginLeft: '10px',
              }}
            >
              Choisir un donateur
            </a>

            <span
              className={stylesList.danger}
              style={{ marginLeft: '5px', color: 'red', fontWeight: 'bold' }}
            >
              *
            </span>
          </div>
        )}
      </div>
      {meta &&
        meta.error &&
        meta.error.length > 0 &&
        meta.error[0].donor &&
        meta.error[0].donor.id &&
        selectedDonors.length === 0 &&
        meta.touched && (
          <div
            style={{
              color: '#dc3545',
              fontSize: '90%',

              marginBottom: '25px',
            }}
          >
            {meta.error[0].donor.id}
          </div>
        )}

      {getedSelectedDonors && getedSelectedDonors.length > 1 && (
        <div style={{ marginTop: '20px' }}>
          <p
            style={{
              fontSize: '16px',
              color: '#555',
              marginBottom: '20px',
              fontStyle: 'italic',
            }}
          >
            Chaque donateur participant va avoir sa propre Kafalat avec le
            bénéficiaire choisi.
          </p>
        </div>
      )}

      <ChooseMultiDonorModal
        show={showModal}
        onHide={() => setShowModal(false)}
        formProps={formProps}
        onConfirmSelection={handleConfirmSelection}
        type={type}
        selectedDonors={selectedDonors}
        getedSelectedDonors={getedSelectedDonors}
      />

      <div>
        <DonorsCard
          disabled={disabled}
          props={getedSelectedDonors}
          handleRemoveDonor={id => {
            handleConfirmSelection(
              selectedDonors.filter(donor => donor.id !== id),
            );
          }}
        />
      </div>
    </div>
  );
}

const DonorsCard = ({ props, handleRemoveDonor, disabled }) => {
  const getedSelectedDonors = props;
  console.log('getedSelectedDonors', getedSelectedDonors);
  return (
    <div className="d-flex flex-wrap justify-content-center">
      {getedSelectedDonors.map(donor => (
        <Card
          key={donor.id}
          className="shadow mx-auto my-4 position-relative" // Add position-relative to enable absolute positioning
          style={{
            width: '400px', // Fixed width for consistent card size
            borderRadius: '12px',
            overflow: 'hidden',
            background: 'linear-gradient(135deg, #f7f8fa, #e9ecef)', // Light gradient background
            marginRight: '20px',
          }}
        >
          {/* Remove the button's div from here */}
          <div className="d-flex align-items-center justify-content-center mt-3">
            <Card.Img
              src={
                donor.picture64 == null || donor.picture64 === ''
                  ? donor.type === 'Moral'
                    ? COMAPNY_PICTURE
                    : image
                  : `data:image/png;base64,${atob(donor.picture64)}`
              }
              className="rounded-circle"
              alt="Donor"
              style={{
                width: '100px',
                height: '100px',
                border: '2px solid white',
              }}
            />
          </div>
          <Card.Body className="text-center">
            <div className="d-flex justify-content-center flex-column">
              <div className="d-flex justify-content-between">
                <div className="text-left">
                  <div className="d-flex">
                    <p
                      className="font-weight-bold mb-0"
                      style={{ minWidth: '80px' }}
                    >
                      Nom{' '}
                      {donor.type === 'Moral' ? "de l'entreprise" : 'complet'} :
                    </p>
                    <p
                      className="ml-2 text-truncate"
                      style={{
                        maxWidth: '250px', // Limit width for truncation effect
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {donor.name}
                    </p>
                  </div>
                  <div className="d-flex">
                    <p
                      className="font-weight-bold mb-0"
                      style={{ minWidth: '80px' }}
                    >
                      Type :
                    </p>
                    <p className="ml-2">{donor.type}</p>
                  </div>
                   <div className="d-flex">
                    <p
                      className="font-weight-bold mb-0"
                      style={{ minWidth: '80px' }}
                    >
                      Solde d'emprunt :
                    </p>
                    <p className="ml-2">{donor.empruntSold > 0 ? `${donor.empruntSold} DH` : "0 DH"}</p>
                  </div>
                  {donor.empruntSold > 0 && (
                    <div className="mt-2">
                      <p
                        className="mb-0"
                        style={{
                          fontSize: '12px',
                          fontStyle: 'italic',
                          backgroundColor: '#f8d7da',
                          padding: '5px 8px',
                          borderRadius: '4px',
                          color:'#721c24',
                          border: '1px solid #f8d7da'
                        }}
                      >
                        ⚠️ Ce montant sera déduit du don
                      </p>
                    </div>
                  )}
                </div>
              </div>
              <Link
                to={`/donors/fiche/${donor.id}/info`}
                style={{
                  color: '#4F89D7',
                  textDecoration: 'underline',
                  cursor: 'pointer',
                  transition: 'color 0.3s',
                  display: 'flex',
                  alignItems: 'center',
                  marginTop: '10px',
                }}
              >
                <img src={infoIcon} width="18px" height="18px" alt="Info" />
                <span className="ml-2">Plus de détails</span>
              </Link>

              {/* Conditional rendering for 'Retirer' button */}
              {!(disabled || getedSelectedDonors.length === 1) && (
                <div
                  className="position-absolute"
                  style={{ top: '10px', right: '10px' }}
                >
                  <Button
                    variant="outline-danger"
                    onClick={() => handleRemoveDonor(donor.id)}
                  >
                    Retirer
                  </Button>
                </div>
              )}
            </div>
          </Card.Body>
        </Card>
      ))}
    </div>
  );
};

import { call, put, takeLatest } from 'redux-saga/effects';
import request from 'utils/request';
import {
  LOAD_CAISSES,
  ADD_CAISSE_REQUEST,
  UPDATE_CAISSE_REQUEST,
  DELETE_CAISSE,
  LOAD_CAISSE_BY_ID,
} from './constants';
import {
  loadCaissesSuccess,
  loadCaissesError,
  deleteCaisseSuccess,
  addCaisseSuccess,
  addCaisseError,
  updateCaisseSuccess,
  updateCaisseError,
  loadCaisseByIdSuccess,
  loadCaisseByIdError,
} from './actions';

function* fetchCaisses(action) {
  try {
    const page = action.page || 0;
    const filters = action.filters || {};
    let url = `/caisses-emprunt?page=${page}&size=10`;

    // Add filter parameters if they exist
    if (filters.searchByDonorName) {
      url += `&searchByDonorName=${encodeURIComponent(filters.searchByDonorName)}`;
    }
    if (filters.searchByStatus) {
      url += `&searchByStatus=${encodeURIComponent(filters.searchByStatus)}`;
    }
    if (filters.searchByDateEmprunt) {
      // Convert date to ISO datetime format for backend
      const dateEmprunt = new Date(filters.searchByDateEmprunt);
      dateEmprunt.setHours(0, 0, 0, 0); // Start of day
      url += `&searchByDateEmprunt=${dateEmprunt.toISOString()}`;
    }
    if (filters.searchByDateRemboursement) {
      // Convert date to ISO datetime format for backend
      const dateRemboursement = new Date(filters.searchByDateRemboursement);
      dateRemboursement.setHours(0, 0, 0, 0); // Start of day
      url += `&searchByDateRemboursement=${dateRemboursement.toISOString()}`;
    }

    const { data } = yield call(request.get, url);
    yield put(loadCaissesSuccess(data));
  } catch (error) {
    yield put(loadCaissesError(error));
  }
}

function* addCaisse(action) {
  const url = '/caisses-emprunt';
  try {
    // Transform the form data to match the required DTO structure
    const { donor, amount, dateEmprunt, description, serviceCategoryId, serviceId } = action.payload;

    // Transform date to local datetime (add current time to the date)
    const dateWithTime = new Date(dateEmprunt);
    const now = new Date();
    dateWithTime.setHours(now.getHours(), now.getMinutes(), now.getSeconds());

    const requestBody = {
      donorId: donor.id,
      globalAmount: parseFloat(amount),
      lastDateEmprunt: dateWithTime.toISOString(),
      description: description || '',
      serviceId: serviceId ? parseInt(serviceId) : null,
      serviceCategoryId: serviceCategoryId ? parseInt(serviceCategoryId) : null,
      histories: [
        {
          amount: parseFloat(amount),
          dateEmprunt: dateWithTime.toISOString(),
          type: 'EMPRUNT',
          status: 'ACTIVE',
          description: description || '',
          serviceId: serviceId ? parseInt(serviceId) : null,
          serviceCategoryId: serviceCategoryId ? parseInt(serviceCategoryId) : null,
        }
      ]
    };

    const { data } = yield call(request.post, url, requestBody);
    yield put(addCaisseSuccess(data));
  } catch (error) {
    yield put(addCaisseError(error));
  }
}

function* updateCaisse(action) {
  const url = `/caisses-emprunt/${action.payload.id}`;
  try {
    // Transform the form data to match the required DTO structure
    const { donor, amount, dateEmprunt, description, serviceCategoryId, serviceId } = action.payload;

    // Transform date to local datetime (add current time to the date)
    const dateWithTime = new Date(dateEmprunt);
    const now = new Date();
    dateWithTime.setHours(now.getHours(), now.getMinutes(), now.getSeconds());

    const requestBody = {
      donorId: donor.id,
      globalAmount: parseFloat(amount),
      lastDateEmprunt: dateWithTime.toISOString(),
      description: description || '',
      serviceId: serviceId ? parseInt(serviceId) : null,
      serviceCategoryId: serviceCategoryId ? parseInt(serviceCategoryId) : null,
    };

    const { data } = yield call(request.put, url, requestBody);
    yield put(updateCaisseSuccess(data));
  } catch (error) {
    yield put(updateCaisseError(error));
  }
}

function* deleteCaisse(action) {
  const url = `/caisses-emprunt/${action.id}`;
  try {
    yield call(request.delete, url);
    yield put(deleteCaisseSuccess(action.id));
  } catch (error) {
    yield put(loadCaissesError(error));
  }
}

function* loadCaisseById(action) {
  const url = `/caisses-emprunt/${action.id}`;
  try {
    const { data } = yield call(request.get, url);
    yield put(loadCaisseByIdSuccess(data));
  } catch (error) {
    yield put(loadCaisseByIdError(error));
  }
}

export default function* communCaisseSaga() {
  yield takeLatest(LOAD_CAISSES, fetchCaisses);
  yield takeLatest(ADD_CAISSE_REQUEST, addCaisse);
  yield takeLatest(UPDATE_CAISSE_REQUEST, updateCaisse);
  yield takeLatest(DELETE_CAISSE, deleteCaisse);
  yield takeLatest(LOAD_CAISSE_BY_ID, loadCaisseById);
}
